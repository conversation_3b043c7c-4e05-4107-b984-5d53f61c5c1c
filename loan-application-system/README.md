# Nira - Loan Application System

A modern, mobile-first loan application system built with Next.js 15, TypeScript, and Tailwind CSS. This application provides a seamless multi-step loan application process with real-time validation and progress tracking.

## 🚀 Features

- **Mobile-First Design**: Optimized for mobile devices with responsive design
- **Multi-Step Flow**: 5-step loan application process with progress tracking
- **Real-Time Validation**: Form validation with immediate feedback
- **OTP Verification**: Simulated phone number verification with OTP
- **PAN Verification**: PAN card details verification and confirmation
- **Employment Details**: Comprehensive employment information collection
- **Address Management**: Address collection with state dropdown
- **Data Persistence**: Application data stored in localStorage
- **Modern UI**: Clean, professional interface with Tailwind CSS

## 📱 Application Flow

### 1. Welcome Screen (`/`)
- Pre-approved loan offer display
- Loan amount: ₹3,50,000 at 24.96% interest
- Key benefits and features
- "Apply Now" CTA button

### 2. Phone Verification (`/loan-application`)
- Mobile number input with +91 prefix
- Real-time phone number formatting
- Send OTP functionality

### 3. OTP Verification (`/loan-application/phone`)
- 6-digit OTP input with auto-focus
- 30-second resend timer
- Individual digit input fields

### 4. PAN Details (`/loan-application/pan`)
- PAN number input with formatting (**********)
- Full name as per PAN card
- Real-time PAN validation

### 5. PAN Confirmation (`/loan-application/pan-confirm`)
- Review and confirm PAN details
- Display formatted information
- Edit option available

### 6. Employment Details (`/loan-application/employment`)
- Employment type selection
- Company name and designation
- Monthly income input
- Work experience and company type

### 7. Address Details (`/loan-application/address`)
- Current/Permanent address selection
- Complete address form with state dropdown
- Pincode validation

### 8. Success Page (`/loan-application/success`)
- Application confirmation
- Generated application ID
- Next steps information
- Download application copy option

## 🛠 Tech Stack

- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Development**: Turbopack for fast builds
- **Linting**: ESLint with Next.js configuration
- **Fonts**: Geist Sans and Geist Mono

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd loan-application-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/
│   ├── loan-application/
│   │   ├── page.tsx              # Phone verification
│   │   ├── phone/page.tsx        # OTP verification
│   │   ├── pan/page.tsx          # PAN details
│   │   ├── pan-confirm/page.tsx  # PAN confirmation
│   │   ├── employment/page.tsx   # Employment details
│   │   ├── address/page.tsx      # Address details
│   │   └── success/page.tsx      # Success page
│   ├── layout.tsx                # Root layout
│   ├── page.tsx                  # Welcome page
│   └── globals.css               # Global styles
└── components/
    └── LoanApplicationLayout.tsx # Shared layout component
```

## 🎨 Design Features

- **Orange/Red Color Scheme**: Matches the original design
- **Progress Indicators**: Visual progress bar with step tracking
- **Responsive Design**: Works seamlessly on mobile and desktop
- **Form Validation**: Real-time validation with error messages
- **Loading States**: Button loading states during form submission
- **Success Feedback**: Visual confirmation and next steps

## 🔧 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📝 Data Flow

The application uses localStorage to persist user data across steps:

```javascript
{
  phoneNumber: "9048739567",
  phoneVerified: true,
  otp: "123456",
  panNumber: "**********",
  fullName: "John Doe",
  panConfirmed: true,
  employment: {
    employmentType: "Salaried",
    companyName: "Tech Corp",
    designation: "Software Engineer",
    monthlyIncome: "50000",
    workExperience: "2",
    companyType: "Private Limited"
  },
  address: {
    addressLine1: "123 Main Street",
    addressLine2: "Near Park",
    city: "Bangalore",
    state: "Karnataka",
    pincode: "560001",
    addressType: "Current"
  },
  applicationComplete: true
}
```

## 🚀 Deployment

This project can be deployed on Vercel, Netlify, or any platform that supports Next.js:

1. **Vercel** (Recommended)
   ```bash
   npm run build
   vercel --prod
   ```

2. **Build for static export**
   ```bash
   npm run build
   npm run export
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
