import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <span className="text-orange-500 font-bold text-lg">Nira</span>
        </div>
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">₹3,50,000</span>
          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 px-4 py-6">
        {/* Congratulations Card */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-start gap-3 mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-xl">🎉</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900 mb-1">
                You are pre-approved for loan upto
              </h1>
              <div className="flex items-baseline gap-2">
                <span className="text-2xl font-bold text-gray-900">₹3,50,000</span>
                <span className="text-sm text-gray-500">at 24.96%</span>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 rounded-lg p-4 mb-6">
            <h2 className="font-semibold text-gray-900 mb-3">CONGRATULATIONS SRIHARI</h2>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">24 hour disbursal - no paper documents required</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">No prepayment penalty</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Paperless process</span>
              </div>
            </div>
          </div>

          <Link
            href="/loan-application"
            className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium text-center block hover:bg-orange-600 transition-colors"
          >
            APPLY NOW
          </Link>
        </div>

        {/* Features */}
        <div className="space-y-3">
          <div className="flex items-center gap-3 text-sm text-gray-600">
            <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-xs">📱</span>
            </span>
            <span>100% paperless process</span>
          </div>
          <div className="flex items-center gap-3 text-sm text-gray-600">
            <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-xs">⚡</span>
            </span>
            <span>Instant approval</span>
          </div>
        </div>
      </main>

      {/* Bottom Navigation */}
      <div className="bg-white border-t px-4 py-3">
        <div className="flex justify-center gap-8">
          <div className="flex flex-col items-center">
            <div className="w-6 h-6 bg-blue-500 rounded-full mb-1"></div>
            <span className="text-xs text-blue-500">Home</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-6 h-6 bg-gray-300 rounded-full mb-1"></div>
            <span className="text-xs text-gray-400">Loans</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-6 h-6 bg-gray-300 rounded-full mb-1"></div>
            <span className="text-xs text-gray-400">Profile</span>
          </div>
        </div>
      </div>
    </div>
  );
}
