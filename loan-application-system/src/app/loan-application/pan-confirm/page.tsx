"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

export default function PANConfirmation() {
  const [applicationData, setApplicationData] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const data = JSON.parse(localStorage.getItem("loanApplication") || "{}");
    setApplicationData(data);
  }, []);

  const handleConfirm = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update application data
    const existingData = JSON.parse(localStorage.getItem("loanApplication") || "{}");
    localStorage.setItem("loanApplication", JSON.stringify({
      ...existingData,
      panConfirmed: true
    }));
    
    router.push("/loan-application/employment");
  };

  const handleEdit = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        <button 
          onClick={() => router.back()}
          className="w-8 h-8 flex items-center justify-center"
        >
          <span className="text-gray-600">←</span>
        </button>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <span className="text-orange-500 font-bold text-lg">Nira</span>
        </div>
        <div className="w-8 h-8"></div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white px-4 py-2">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Step 3 of 5</span>
          <span className="text-sm text-gray-600">60%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-orange-500 h-2 rounded-full w-3/5"></div>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Please confirm your PAN details
          </h1>
          <p className="text-gray-600 mb-8">
            Make sure all details are correct before proceeding
          </p>

          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">
                  PAN Number
                </label>
                <div className="text-lg font-mono tracking-wider text-gray-900">
                  {applicationData.panNumber || "**********"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">
                  Full Name
                </label>
                <div className="text-lg text-gray-900">
                  {applicationData.fullName || "Full Name"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">
                  Date of Birth
                </label>
                <div className="text-lg text-gray-900">
                  15 Jan, 1990
                </div>
              </div>
            </div>

            <button
              onClick={handleEdit}
              className="mt-4 text-orange-500 font-medium text-sm"
            >
              Edit Details
            </button>
          </div>

          <div className="space-y-4">
            <button
              onClick={handleConfirm}
              disabled={isLoading}
              className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-orange-600 transition-colors"
            >
              {isLoading ? "CONFIRMING..." : "CONFIRM DETAILS"}
            </button>
          </div>

          <div className="mt-8 space-y-3">
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 text-xs">✓</span>
              </span>
              <span>PAN details verified successfully</span>
            </div>
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-xs">🔒</span>
              </span>
              <span>Your information is secure and encrypted</span>
            </div>
          </div>

          <div className="mt-6 bg-blue-50 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <span className="text-blue-600 text-sm">ℹ️</span>
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Next Step</p>
                <p>We'll need your employment details to process your loan application.</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
