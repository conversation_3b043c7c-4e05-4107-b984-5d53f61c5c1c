"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

export default function PANVerification() {
  const [panNumber, setPanNumber] = useState("");
  const [fullName, setFullName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const formatPAN = (value: string) => {
    // Remove all non-alphanumeric characters and convert to uppercase
    const cleaned = value.replace(/[^A-Z0-9]/gi, '').toUpperCase();
    
    // Format as **********
    if (cleaned.length <= 5) {
      return cleaned;
    } else if (cleaned.length <= 9) {
      return cleaned.slice(0, 5) + cleaned.slice(5);
    } else {
      return cleaned.slice(0, 5) + cleaned.slice(5, 9) + cleaned.slice(9, 10);
    }
  };

  const isValidPAN = (pan: string) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan);
  };

  const handlePANChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPAN(e.target.value);
    if (formatted.length <= 10) {
      setPanNumber(formatted);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isValidPAN(panNumber) || !fullName.trim()) return;
    
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update application data
    const existingData = JSON.parse(localStorage.getItem("loanApplication") || "{}");
    localStorage.setItem("loanApplication", JSON.stringify({
      ...existingData,
      panNumber,
      fullName: fullName.trim()
    }));
    
    router.push("/loan-application/pan-confirm");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        <button 
          onClick={() => router.back()}
          className="w-8 h-8 flex items-center justify-center"
        >
          <span className="text-gray-600">←</span>
        </button>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <span className="text-orange-500 font-bold text-lg">Nira</span>
        </div>
        <div className="w-8 h-8"></div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white px-4 py-2">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Step 3 of 5</span>
          <span className="text-sm text-gray-600">60%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-orange-500 h-2 rounded-full w-3/5"></div>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Is this your PAN number?
          </h1>
          <p className="text-gray-600 mb-8">
            Please enter your PAN details as per your PAN card
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                PAN Number
              </label>
              <input
                type="text"
                value={panNumber}
                onChange={handlePANChange}
                placeholder="**********"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-lg font-mono tracking-wider"
                required
              />
              {panNumber && !isValidPAN(panNumber) && (
                <p className="text-red-500 text-sm mt-1">Please enter a valid PAN number</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Name (as per PAN)
              </label>
              <input
                type="text"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                placeholder="Enter your full name"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-lg"
                required
              />
            </div>

            <button
              type="submit"
              disabled={!isValidPAN(panNumber) || !fullName.trim() || isLoading}
              className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-orange-600 transition-colors"
            >
              {isLoading ? "VERIFYING..." : "VERIFY DETAILS"}
            </button>
          </form>

          <div className="mt-8 space-y-3">
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-xs">🔒</span>
              </span>
              <span>Your PAN details are encrypted and secure</span>
            </div>
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-xs">✓</span>
              </span>
              <span>We verify your details with government databases</span>
            </div>
          </div>

          <div className="mt-6 bg-yellow-50 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <span className="text-yellow-600 text-sm">⚠️</span>
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">Important Note</p>
                <p>Please ensure your PAN details match exactly with your PAN card to avoid verification delays.</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
