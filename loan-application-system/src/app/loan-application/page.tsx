"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

export default function PhoneVerification() {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (phoneNumber.length !== 10) return;
    
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Store phone number in localStorage for the flow
    localStorage.setItem("loanApplication", JSON.stringify({ phoneNumber }));
    
    router.push("/loan-application/phone");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        <button 
          onClick={() => router.back()}
          className="w-8 h-8 flex items-center justify-center"
        >
          <span className="text-gray-600">←</span>
        </button>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <span className="text-orange-500 font-bold text-lg">Nira</span>
        </div>
        <div className="w-8 h-8"></div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white px-4 py-2">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Step 1 of 5</span>
          <span className="text-sm text-gray-600">20%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-orange-500 h-2 rounded-full w-1/5"></div>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Please Number Verification
          </h1>
          <p className="text-gray-600 mb-8">
            We'll send you a verification code
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mobile Number
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <span className="text-gray-500 text-sm">+91</span>
                </div>
                <input
                  type="tel"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, '').slice(0, 10))}
                  placeholder="9048739567"
                  className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-lg"
                  required
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={phoneNumber.length !== 10 || isLoading}
              className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-orange-600 transition-colors"
            >
              {isLoading ? "SENDING..." : "SEND OTP"}
            </button>
          </form>

          <div className="mt-8 space-y-3">
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-xs">🔒</span>
              </span>
              <span>Your data is safe and secure with us</span>
            </div>
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-xs">⚡</span>
              </span>
              <span>Quick verification process</span>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
