"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function ApplicationSuccess() {
  const [applicationData, setApplicationData] = useState<any>({});
  const router = useRouter();

  useEffect(() => {
    const data = JSON.parse(localStorage.getItem("loanApplication") || "{}");
    setApplicationData(data);
    
    // If no application data, redirect to start
    if (!data.applicationComplete) {
      router.push("/");
    }
  }, [router]);

  const generateApplicationId = () => {
    return "NIRA" + Date.now().toString().slice(-8);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <span className="text-orange-500 font-bold text-lg">Nira</span>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto text-center">
          {/* Success Icon */}
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-green-600 text-3xl">✓</span>
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Application Submitted Successfully!
          </h1>
          
          <p className="text-gray-600 mb-8">
            Your loan application has been received and is being processed.
          </p>

          {/* Application Details Card */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6 text-left">
            <h2 className="font-semibold text-gray-900 mb-4">Application Details</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Application ID</span>
                <span className="font-medium text-gray-900">{generateApplicationId()}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Loan Amount</span>
                <span className="font-medium text-gray-900">₹3,50,000</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Interest Rate</span>
                <span className="font-medium text-gray-900">24.96% p.a.</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Processing Time</span>
                <span className="font-medium text-gray-900">24 hours</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Status</span>
                <span className="font-medium text-orange-600">Under Review</span>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-semibold text-blue-900 mb-3">What happens next?</h3>
            <div className="space-y-2 text-sm text-blue-800">
              <div className="flex items-start gap-2">
                <span className="w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">1</span>
                <span>We'll verify your documents and details</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">2</span>
                <span>You'll receive approval notification within 24 hours</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">3</span>
                <span>Loan amount will be disbursed to your bank account</span>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-orange-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-orange-900 mb-2">Need Help?</h3>
            <div className="text-sm text-orange-800 space-y-1">
              <p>📞 Call us: 1800-123-4567</p>
              <p>💬 WhatsApp: +91 98765 43210</p>
              <p>📧 Email: <EMAIL></p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Link
              href="/"
              className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium block text-center hover:bg-orange-600 transition-colors"
            >
              GO TO HOME
            </Link>
            
            <button
              onClick={() => {
                const data = localStorage.getItem("loanApplication");
                if (data) {
                  const blob = new Blob([data], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = 'loan-application.json';
                  a.click();
                  URL.revokeObjectURL(url);
                }
              }}
              className="w-full border border-orange-500 text-orange-500 py-3 px-4 rounded-lg font-medium hover:bg-orange-50 transition-colors"
            >
              DOWNLOAD APPLICATION COPY
            </button>
          </div>

          {/* Important Note */}
          <div className="mt-8 text-xs text-gray-500 text-center">
            <p>Keep your application ID safe for future reference.</p>
            <p>You'll receive SMS and email updates on your registered contact details.</p>
          </div>
        </div>
      </main>
    </div>
  );
}
