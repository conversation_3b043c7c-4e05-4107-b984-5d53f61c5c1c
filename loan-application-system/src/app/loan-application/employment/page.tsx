"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

export default function EmploymentDetails() {
  const [formData, setFormData] = useState({
    employmentType: "",
    companyName: "",
    designation: "",
    monthlyIncome: "",
    workExperience: "",
    companyType: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const employmentTypes = [
    "Salaried",
    "Self Employed",
    "Business Owner",
    "Freelancer"
  ];

  const companyTypes = [
    "Private Limited",
    "Public Limited", 
    "Government",
    "MNC",
    "Startup",
    "Other"
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const requiredFields = ['employmentType', 'companyName', 'designation', 'monthlyIncome'];
    const isValid = requiredFields.every(field => formData[field as keyof typeof formData].trim());
    
    if (!isValid) return;
    
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update application data
    const existingData = JSON.parse(localStorage.getItem("loanApplication") || "{}");
    localStorage.setItem("loanApplication", JSON.stringify({
      ...existingData,
      employment: formData
    }));
    
    router.push("/loan-application/address");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        <button 
          onClick={() => router.back()}
          className="w-8 h-8 flex items-center justify-center"
        >
          <span className="text-gray-600">←</span>
        </button>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <span className="text-orange-500 font-bold text-lg">Nira</span>
        </div>
        <div className="w-8 h-8"></div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white px-4 py-2">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Step 4 of 5</span>
          <span className="text-sm text-gray-600">80%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-orange-500 h-2 rounded-full w-4/5"></div>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Please enter your employment details
          </h1>
          <p className="text-gray-600 mb-8">
            This helps us understand your income and approve your loan
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Employment Type *
              </label>
              <select
                value={formData.employmentType}
                onChange={(e) => handleInputChange('employmentType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                required
              >
                <option value="">Select employment type</option>
                {employmentTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Name *
              </label>
              <input
                type="text"
                value={formData.companyName}
                onChange={(e) => handleInputChange('companyName', e.target.value)}
                placeholder="Enter your company name"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Designation *
              </label>
              <input
                type="text"
                value={formData.designation}
                onChange={(e) => handleInputChange('designation', e.target.value)}
                placeholder="e.g. Software Engineer"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Monthly Income *
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
                <input
                  type="number"
                  value={formData.monthlyIncome}
                  onChange={(e) => handleInputChange('monthlyIncome', e.target.value)}
                  placeholder="50,000"
                  className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Work Experience (Years)
              </label>
              <input
                type="number"
                value={formData.workExperience}
                onChange={(e) => handleInputChange('workExperience', e.target.value)}
                placeholder="2"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Type
              </label>
              <select
                value={formData.companyType}
                onChange={(e) => handleInputChange('companyType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Select company type</option>
                {companyTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-orange-600 transition-colors"
            >
              {isLoading ? "SAVING..." : "CONTINUE"}
            </button>
          </form>
        </div>
      </main>
    </div>
  );
}
