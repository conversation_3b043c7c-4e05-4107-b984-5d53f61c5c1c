@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Force light mode for the loan application */
body {
  background: #ffffff;
  color: #171717;
  font-family: Arial, Helvetica, sans-serif;
  /* Mobile-first responsive design */
  max-width: 100vw;
  overflow-x: hidden;
}

/* Override any dark mode styles */
* {
  color-scheme: light;
}

/* Ensure input text is always dark */
input, textarea, select {
  color: #171717 !important;
  background-color: #ffffff !important;
}

/* Mobile-like container for web */
@media (min-width: 768px) {
  body {
    display: flex;
    justify-content: center;
    background: #f3f4f6;
    padding: 20px 0;
  }

  body > div {
    max-width: 375px;
    width: 100%;
    background: #ffffff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    min-height: calc(100vh - 40px);
  }
}

/* Security styling for sensitive inputs */
.secure-input {
  font-family: monospace;
  letter-spacing: 2px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  position: relative;
}

.secure-input:focus {
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

/* Validation error styles */
.error-input {
  border-color: #ef4444 !important;
  background-color: #fef2f2 !important;
}

.error-text {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Success validation styles */
.success-input {
  border-color: #10b981 !important;
  background-color: #f0fdf4 !important;
}

/* Loading overlay for security */
.security-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
