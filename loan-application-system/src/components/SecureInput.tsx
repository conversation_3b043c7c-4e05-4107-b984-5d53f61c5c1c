'use client';

import React, { useState, useEffect } from 'react';
import { ValidationResult } from '@/utils/validation';
import { useSecurityContext } from '@/contexts/SecurityContext';

interface SecureInputProps {
  type: 'text' | 'tel' | 'email' | 'password' | 'number';
  value: string;
  onChange: (value: string) => void;
  onValidation?: (result: ValidationResult) => void;
  validator?: (value: string) => ValidationResult;
  placeholder?: string;
  label?: string;
  required?: boolean;
  maxLength?: number;
  className?: string;
  isSecure?: boolean;
  autoComplete?: string;
  disabled?: boolean;
}

export const SecureInput: React.FC<SecureInputProps> = ({
  type,
  value,
  onChange,
  onValidation,
  validator,
  placeholder,
  label,
  required = false,
  maxLength,
  className = '',
  isSecure = false,
  autoComplete = 'off',
  disabled = false
}) => {
  const [validationResult, setValidationResult] = useState<ValidationResult>({ isValid: true });
  const [isFocused, setIsFocused] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const { logSecurityEvent, isRateLimited } = useSecurityContext();

  useEffect(() => {
    if (validator && value) {
      const result = validator(value);
      setValidationResult(result);
      onValidation?.(result);

      if (!result.isValid && result.severity === 'high') {
        logSecurityEvent(`Invalid input detected: ${result.error}`, 'high');
      }
    }
  }, [value, validator, onValidation, logSecurityEvent]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Rate limiting for security
    if (isRateLimited('input_change')) {
      logSecurityEvent('Rate limit exceeded for input changes', 'high');
      return;
    }

    // Track failed attempts
    if (validator) {
      const result = validator(newValue);
      if (!result.isValid) {
        setAttempts(prev => prev + 1);
        if (attempts > 5) {
          logSecurityEvent('Multiple validation failures detected', 'medium');
        }
      } else {
        setAttempts(0);
      }
    }

    onChange(newValue);
  };

  const handleFocus = () => {
    setIsFocused(true);
    if (isSecure) {
      logSecurityEvent('Secure input focused', 'low');
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    if (isSecure && value) {
      logSecurityEvent('Secure input completed', 'low');
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    if (isSecure) {
      logSecurityEvent('Paste attempt on secure input', 'medium');
      e.preventDefault();
    }
  };

  const handleCopy = (e: React.ClipboardEvent) => {
    if (isSecure) {
      logSecurityEvent('Copy attempt on secure input', 'high');
      e.preventDefault();
    }
  };

  const getInputClassName = () => {
    let baseClass = `w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 transition-all duration-200 ${className}`;
    
    if (isSecure) {
      baseClass += ' secure-input';
    }
    
    if (validationResult.isValid === false) {
      baseClass += ' error-input';
    } else if (validationResult.isValid && value) {
      baseClass += ' success-input';
    } else {
      baseClass += ' border-gray-300 focus:border-orange-500';
    }
    
    if (disabled) {
      baseClass += ' bg-gray-100 cursor-not-allowed';
    }
    
    return baseClass;
  };

  const getSeverityIcon = (severity?: string) => {
    switch (severity) {
      case 'high':
        return '🚨';
      case 'medium':
        return '⚠️';
      case 'low':
        return 'ℹ️';
      default:
        return '❌';
    }
  };

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
          {isSecure && (
            <span className="ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
              🔒 Secure
            </span>
          )}
        </label>
      )}
      
      <div className="relative">
        <input
          type={type}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onPaste={handlePaste}
          onCopy={handleCopy}
          placeholder={placeholder}
          required={required}
          maxLength={maxLength}
          autoComplete={autoComplete}
          disabled={disabled}
          className={getInputClassName()}
          spellCheck={false}
          autoCorrect="off"
          autoCapitalize="off"
        />
        
        {/* Validation status indicator */}
        {value && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {validationResult.isValid ? (
              <span className="text-green-500 text-lg">✓</span>
            ) : (
              <span className="text-red-500 text-lg">✗</span>
            )}
          </div>
        )}
        
        {/* Security indicator */}
        {isSecure && isFocused && (
          <div className="absolute -top-2 -right-2 w-4 h-4 bg-orange-500 rounded-full animate-pulse"></div>
        )}
      </div>
      
      {/* Validation error message */}
      {!validationResult.isValid && validationResult.error && (
        <div className="error-text">
          <span>{getSeverityIcon(validationResult.severity)}</span>
          <span>{validationResult.error}</span>
        </div>
      )}
      
      {/* Security warning for multiple attempts */}
      {attempts > 3 && (
        <div className="text-orange-600 text-sm flex items-center gap-2">
          <span>⚠️</span>
          <span>Multiple validation attempts detected. Please verify your input.</span>
        </div>
      )}
      
      {/* Character count for secure inputs */}
      {isSecure && maxLength && (
        <div className="text-xs text-gray-500 text-right">
          {value.length}/{maxLength}
        </div>
      )}
    </div>
  );
};
