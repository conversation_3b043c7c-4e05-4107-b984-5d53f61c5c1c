'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import LoanApplicationLayout from './LoanApplicationLayout';

export default function KYCVerification() {
  const router = useRouter();
  const [selectedDocument, setSelectedDocument] = useState<string>('');

  const documents = [
    { id: 'aadhaar', name: 'Aadhaar Card', icon: '🆔' },
    { id: 'pan', name: 'PAN Card', icon: '📄' },
    { id: 'passport', name: 'Passport', icon: '📘' },
    { id: 'driving-license', name: 'Driving License', icon: '🚗' },
    { id: 'voter-id', name: 'Voter ID', icon: '🗳️' }
  ];

  const handleDocumentSelect = (docId: string) => {
    setSelectedDocument(docId);
  };

  const handleContinue = () => {
    if (selectedDocument) {
      router.push('/kyc-upload');
    }
  };

  return (
    <LoanApplicationLayout
      step={5}
      totalSteps={8}
      title="Enter PAN code"
      subtitle="For KYC verification"
    >
      <div className="space-y-6">
        {/* PAN Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            PAN Number
          </label>
          <input
            type="text"
            placeholder="Enter your PAN number"
            className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 uppercase"
            maxLength={10}
          />
        </div>

        {/* Document Selection */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Select your bank for Verification
          </h3>
          <div className="space-y-3">
            {documents.map((doc) => (
              <button
                key={doc.id}
                onClick={() => handleDocumentSelect(doc.id)}
                className={`w-full p-4 rounded-lg border-2 transition-all ${
                  selectedDocument === doc.id
                    ? 'border-orange-500 bg-orange-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-lg">
                    {doc.icon}
                  </div>
                  <span className="text-gray-900 font-medium">{doc.name}</span>
                  {selectedDocument === doc.id && (
                    <div className="ml-auto">
                      <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Continue button */}
        <button
          onClick={handleContinue}
          disabled={!selectedDocument}
          className={`w-full py-3 rounded-lg font-medium transition-colors ${
            selectedDocument
              ? 'bg-orange-500 text-white hover:bg-orange-600'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          CONTINUE
        </button>
      </div>
    </LoanApplicationLayout>
  );
}
