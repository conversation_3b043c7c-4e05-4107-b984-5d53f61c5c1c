'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoanApplicationLayout from './LoanApplicationLayout';

export default function KYCLoading() {
  const router = useRouter();

  useEffect(() => {
    // Simulate loading time and redirect
    const timer = setTimeout(() => {
      router.push('/kyc-photo');
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <LoanApplicationLayout
      step={6}
      totalSteps={8}
      title="Fetching your KYC details"
      subtitle="This should take a few seconds..."
      showBackButton={false}
    >
      <div className="flex flex-col items-center justify-center py-12">
        {/* Loading animation */}
        <div className="relative mb-8">
          <div className="w-16 h-16 border-4 border-gray-200 rounded-full"></div>
          <div className="absolute top-0 left-0 w-16 h-16 border-4 border-orange-500 rounded-full border-t-transparent animate-spin"></div>
        </div>

        {/* Loading dots */}
        <div className="flex space-x-2 mb-6">
          <div className="w-3 h-3 bg-orange-500 rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-3 h-3 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>

        {/* Status text */}
        <p className="text-gray-600 text-center">
          Please wait while we verify your documents...
        </p>
      </div>
    </LoanApplicationLayout>
  );
}
