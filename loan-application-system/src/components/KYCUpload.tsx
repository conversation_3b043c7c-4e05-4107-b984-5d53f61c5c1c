'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import LoanApplicationLayout from './LoanApplicationLayout';

export default function KYCUpload() {
  const router = useRouter();
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);

  const handleFileUpload = (type: string) => {
    // Simulate file upload
    if (!uploadedFiles.includes(type)) {
      setUploadedFiles([...uploadedFiles, type]);
    }
  };

  const handleContinue = () => {
    router.push('/kyc-loading');
  };

  return (
    <LoanApplicationLayout
      step={6}
      totalSteps={8}
      title="Thanks for providing your details."
      subtitle="We are now verifying your information. This may take up to 2 minutes to complete. Verifying bank will not impact your credit score."
    >
      <div className="space-y-6">
        {/* Upload sections */}
        <div className="space-y-4">
          {/* Aadhaar Upload */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-blue-600 text-xl">📄</span>
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Upload Aadhaar Card</h3>
            <p className="text-sm text-gray-600 mb-4">
              Please upload front and back of your Aadhaar card
            </p>
            <button
              onClick={() => handleFileUpload('aadhaar')}
              className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
            >
              {uploadedFiles.includes('aadhaar') ? 'Uploaded ✓' : 'Upload'}
            </button>
          </div>

          {/* Bank Statement Upload */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-green-600 text-xl">🏦</span>
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Bank Statement</h3>
            <p className="text-sm text-gray-600 mb-4">
              Upload last 3 months bank statement
            </p>
            <button
              onClick={() => handleFileUpload('bank-statement')}
              className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
            >
              {uploadedFiles.includes('bank-statement') ? 'Uploaded ✓' : 'Upload'}
            </button>
          </div>

          {/* Salary Slip Upload */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-purple-600 text-xl">💰</span>
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Salary Slip</h3>
            <p className="text-sm text-gray-600 mb-4">
              Upload last 2 months salary slip
            </p>
            <button
              onClick={() => handleFileUpload('salary-slip')}
              className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
            >
              {uploadedFiles.includes('salary-slip') ? 'Uploaded ✓' : 'Upload'}
            </button>
          </div>
        </div>

        {/* Continue button */}
        <button
          onClick={handleContinue}
          disabled={uploadedFiles.length < 3}
          className={`w-full py-3 rounded-lg font-medium transition-colors ${
            uploadedFiles.length >= 3
              ? 'bg-orange-500 text-white hover:bg-orange-600'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          CONTINUE
        </button>
      </div>
    </LoanApplicationLayout>
  );
}
