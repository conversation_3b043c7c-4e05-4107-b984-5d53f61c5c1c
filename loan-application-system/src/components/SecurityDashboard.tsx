'use client';

import React, { useState, useEffect } from 'react';
import { useSecurityContext } from '@/contexts/SecurityContext';

export const SecurityDashboard: React.FC = () => {
  const [securityLogs, setSecurityLogs] = useState<any[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const { isSecureSession, sessionTimeout } = useSecurityContext();

  useEffect(() => {
    // Load security logs
    const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    setSecurityLogs(logs.slice(-10)); // Show last 10 logs
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'text-red-600 bg-red-50';
      case 'medium':
        return 'text-orange-600 bg-orange-50';
      case 'low':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-600 transition-colors z-50"
        title="Security Dashboard"
      >
        🔒
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-96 overflow-hidden">
        {/* Header */}
        <div className="bg-orange-500 text-white p-4 flex items-center justify-between">
          <h3 className="font-semibold flex items-center gap-2">
            🔒 Security Dashboard
          </h3>
          <button
            onClick={() => setIsVisible(false)}
            className="text-white hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        {/* Security Status */}
        <div className="p-4 border-b">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Session Status:</span>
              <span className={`text-sm px-2 py-1 rounded ${
                isSecureSession ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {isSecureSession ? '🟢 Secure' : '🔴 Insecure'}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Session Timeout:</span>
              <span className="text-sm text-gray-800">
                {Math.round(sessionTimeout / 60000)} minutes
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Data Encryption:</span>
              <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">
                🟢 Active
              </span>
            </div>
          </div>
        </div>

        {/* Security Logs */}
        <div className="p-4">
          <h4 className="font-medium text-gray-900 mb-3">Recent Security Events</h4>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {securityLogs.length === 0 ? (
              <p className="text-sm text-gray-500 text-center py-4">
                No security events recorded
              </p>
            ) : (
              securityLogs.map((log, index) => (
                <div
                  key={index}
                  className={`p-2 rounded text-xs ${getSeverityColor(log.severity)}`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium">
                      {log.severity === 'high' && '🚨'}
                      {log.severity === 'medium' && '⚠️'}
                      {log.severity === 'low' && 'ℹ️'}
                      {log.severity.toUpperCase()}
                    </span>
                    <span className="text-xs opacity-75">
                      {formatTime(log.timestamp)}
                    </span>
                  </div>
                  <p className="text-xs">{log.event}</p>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Security Tips */}
        <div className="bg-gray-50 p-4">
          <h4 className="font-medium text-gray-900 mb-2">Security Tips</h4>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>• Never share your OTP with anyone</li>
            <li>• Ensure you're on a secure connection</li>
            <li>• Don't use public WiFi for sensitive data</li>
            <li>• Close the app when finished</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
