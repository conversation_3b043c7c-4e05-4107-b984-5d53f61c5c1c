'use client';

import { useRouter } from 'next/navigation';

export default function LoanApproved() {
  const router = useRouter();

  const handleAcceptOffer = () => {
    // In a real app, this would process the loan acceptance
    alert('Loan offer accepted! Funds will be disbursed within 24 hours.');
  };

  const handleViewTerms = () => {
    // In a real app, this would show terms and conditions
    alert('Terms and conditions would be displayed here.');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        <button 
          onClick={() => router.back()}
          className="w-8 h-8 flex items-center justify-center"
        >
          <span className="text-gray-600">←</span>
        </button>
        
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <span className="text-orange-500 font-bold text-lg">Nira</span>
        </div>
        <div className="w-8 h-8"></div>
      </header>

      {/* Main Content */}
      <main className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto">
          {/* Success Animation */}
          <div className="text-center py-8">
            <div className="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-4xl">✓</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Congratulations!
            </h1>
            <p className="text-gray-600 mb-8">
              Your loan has been approved
            </p>
          </div>

          {/* Loan Details Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="text-center mb-6">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">₹1,00,000</h2>
              <p className="text-gray-600">Approved Loan Amount</p>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Interest Rate</span>
                <span className="font-semibold text-gray-900">12% per annum</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Loan Tenure</span>
                <span className="font-semibold text-gray-900">24 months</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Monthly EMI</span>
                <span className="font-semibold text-gray-900">₹4,167</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Processing Fee</span>
                <span className="font-semibold text-gray-900">₹1,000</span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-600">Disbursal Amount</span>
                <span className="font-bold text-green-600">₹99,000</span>
              </div>
            </div>
          </div>

          {/* Important Notes */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-blue-900 mb-2">Important:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Funds will be credited within 24 hours</li>
              <li>• First EMI due on 5th of next month</li>
              <li>• Auto-pay is already set up</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleAcceptOffer}
              className="w-full bg-orange-500 text-white py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors"
            >
              ACCEPT OFFER
            </button>
            
            <button
              onClick={handleViewTerms}
              className="w-full border border-gray-300 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              VIEW TERMS & CONDITIONS
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
