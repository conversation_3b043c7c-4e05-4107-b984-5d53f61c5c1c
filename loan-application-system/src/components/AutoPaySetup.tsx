'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import LoanApplicationLayout from './LoanApplicationLayout';

export default function AutoPaySetup() {
  const router = useRouter();
  const [autoPayEnabled, setAutoPayEnabled] = useState(false);

  const handleToggleAutoPay = () => {
    setAutoPayEnabled(!autoPayEnabled);
  };

  const handleContinue = () => {
    router.push('/loan-approved');
  };

  return (
    <LoanApplicationLayout
      step={8}
      totalSteps={8}
      title="Your Auto-Pay is Setup!"
      subtitle="Your loan offer is ready"
    >
      <div className="space-y-6">
        {/* Success icon */}
        <div className="text-center py-8">
          <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-3xl">✓</span>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Setup Complete!
          </h2>
          <p className="text-gray-600">
            Your auto-pay has been successfully configured
          </p>
        </div>

        {/* Auto-pay details */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900">Auto-Pay Settings</h3>
            <button
              onClick={handleToggleAutoPay}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                autoPayEnabled ? 'bg-orange-500' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  autoPayEnabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
          
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Payment Method:</span>
              <span className="text-gray-900">Bank Account (****1234)</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Payment Date:</span>
              <span className="text-gray-900">5th of every month</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Amount:</span>
              <span className="text-gray-900">₹4,167/month</span>
            </div>
          </div>
        </div>

        {/* Benefits */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Benefits of Auto-Pay:</h4>
          <div className="space-y-2">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                <span className="text-green-600 text-xs">✓</span>
              </div>
              <span className="text-sm text-gray-600">Never miss a payment</span>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                <span className="text-green-600 text-xs">✓</span>
              </div>
              <span className="text-sm text-gray-600">Maintain good credit score</span>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                <span className="text-green-600 text-xs">✓</span>
              </div>
              <span className="text-sm text-gray-600">Avoid late payment charges</span>
            </div>
          </div>
        </div>

        {/* Continue button */}
        <button
          onClick={handleContinue}
          className="w-full bg-orange-500 text-white py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors"
        >
          VIEW LOAN OFFER
        </button>
      </div>
    </LoanApplicationLayout>
  );
}
