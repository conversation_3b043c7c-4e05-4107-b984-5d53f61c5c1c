'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import LoanApplicationLayout from './LoanApplicationLayout';

export default function KYCPhoto() {
  const router = useRouter();
  const [photoTaken, setPhotoTaken] = useState(false);

  const handleTakePhoto = () => {
    setPhotoTaken(true);
  };

  const handleContinue = () => {
    router.push('/auto-pay-setup');
  };

  return (
    <LoanApplicationLayout
      step={7}
      totalSteps={8}
      title="Verify Your KYC Details"
      subtitle=""
    >
      <div className="space-y-6">
        {/* User info card */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
              {photoTaken ? (
                <span className="text-2xl">👤</span>
              ) : (
                <span className="text-gray-400 text-2xl">📷</span>
              )}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Sushila Kumari</h3>
              <p className="text-sm text-gray-600">ID: 1234567890</p>
              <p className="text-sm text-gray-600">DOB: 01/01/1990</p>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Instructions:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Take a clear photo of your face</li>
            <li>• Ensure good lighting</li>
            <li>• Remove glasses if wearing any</li>
            <li>• Look directly at the camera</li>
          </ul>
        </div>

        {/* Camera section */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-4xl text-gray-400">📸</span>
          </div>
          <h3 className="font-medium text-gray-900 mb-2">Take a selfie</h3>
          <p className="text-sm text-gray-600 mb-6">
            Position your face in the center and take a clear photo
          </p>
          <button
            onClick={handleTakePhoto}
            className="bg-orange-500 text-white px-8 py-3 rounded-lg hover:bg-orange-600 transition-colors"
          >
            {photoTaken ? 'Retake Photo' : 'Take Photo'}
          </button>
        </div>

        {/* Verification status */}
        {photoTaken && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-white text-sm">✓</span>
            </div>
            <p className="text-green-800 font-medium">Photo captured successfully!</p>
          </div>
        )}

        {/* Continue button */}
        <button
          onClick={handleContinue}
          disabled={!photoTaken}
          className={`w-full py-3 rounded-lg font-medium transition-colors ${
            photoTaken
              ? 'bg-orange-500 text-white hover:bg-orange-600'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          CONTINUE
        </button>
      </div>
    </LoanApplicationLayout>
  );
}
