import { useRouter } from "next/navigation";

interface LoanApplicationLayoutProps {
  children: React.ReactNode;
  step: number;
  totalSteps: number;
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
}

export default function LoanApplicationLayout({
  children,
  step,
  totalSteps,
  title,
  subtitle,
  showBackButton = true
}: LoanApplicationLayoutProps) {
  const router = useRouter();
  const progressPercentage = (step / totalSteps) * 100;

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        {showBackButton ? (
          <button 
            onClick={() => router.back()}
            className="w-8 h-8 flex items-center justify-center"
          >
            <span className="text-gray-600">←</span>
          </button>
        ) : (
          <div className="w-8 h-8"></div>
        )}
        
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <span className="text-orange-500 font-bold text-lg">Nira</span>
        </div>
        <div className="w-8 h-8"></div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white px-4 py-2">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Step {step} of {totalSteps}</span>
          <span className="text-sm text-gray-600">{Math.round(progressPercentage)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-orange-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            {title}
          </h1>
          {subtitle && (
            <p className="text-gray-600 mb-8">
              {subtitle}
            </p>
          )}
          {children}
        </div>
      </main>
    </div>
  );
}
