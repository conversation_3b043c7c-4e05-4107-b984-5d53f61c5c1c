'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import LoanApplicationLayout from './LoanApplicationLayout';

interface Bank {
  id: string;
  name: string;
  logo: string;
  color: string;
}

export default function BankSelection() {
  const router = useRouter();
  const [selectedBank, setSelectedBank] = useState<string>('');

  const banks: Bank[] = [
    { id: 'icici', name: 'ICICI Bank', logo: '🏦', color: 'bg-blue-500' },
    { id: 'hdfc', name: 'HDFC Bank', logo: '🏛️', color: 'bg-red-500' },
    { id: 'sbi', name: 'State Bank of India', logo: '🏢', color: 'bg-blue-600' },
    { id: 'axis', name: 'Axis Bank', logo: '🏪', color: 'bg-purple-500' },
    { id: 'kotak', name: 'Kotak Mahindra Bank', logo: '🏬', color: 'bg-red-600' },
    { id: 'pnb', name: 'Punjab National Bank', logo: '🏭', color: 'bg-orange-600' }
  ];

  const handleBankSelect = (bankId: string) => {
    setSelectedBank(bankId);
  };

  const handleContinue = () => {
    if (selectedBank) {
      router.push('/kyc-verification');
    }
  };

  return (
    <LoanApplicationLayout
      step={4}
      totalSteps={8}
      title="Select your bank for Verification"
      subtitle="We use bank statement to verify your income and eligibility"
    >
      <div className="space-y-6">
        {/* Bank list */}
        <div className="space-y-3">
          {banks.map((bank) => (
            <button
              key={bank.id}
              onClick={() => handleBankSelect(bank.id)}
              className={`w-full p-4 rounded-lg border-2 transition-all ${
                selectedBank === bank.id
                  ? 'border-orange-500 bg-orange-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 ${bank.color} rounded-lg flex items-center justify-center text-white text-lg`}>
                  {bank.logo}
                </div>
                <span className="text-gray-900 font-medium">{bank.name}</span>
                {selectedBank === bank.id && (
                  <div className="ml-auto">
                    <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>
                )}
              </div>
            </button>
          ))}
        </div>

        {/* Other bank option */}
        <button
          onClick={() => handleBankSelect('other')}
          className={`w-full p-4 rounded-lg border-2 transition-all ${
            selectedBank === 'other'
              ? 'border-orange-500 bg-orange-50'
              : 'border-gray-200 bg-white hover:border-gray-300'
          }`}
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center text-white text-lg">
              🏦
            </div>
            <span className="text-gray-900 font-medium">Other Bank</span>
            {selectedBank === 'other' && (
              <div className="ml-auto">
                <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
              </div>
            )}
          </div>
        </button>

        {/* Continue button */}
        <button
          onClick={handleContinue}
          disabled={!selectedBank}
          className={`w-full py-3 rounded-lg font-medium transition-colors ${
            selectedBank
              ? 'bg-orange-500 text-white hover:bg-orange-600'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          CONTINUE
        </button>
      </div>
    </LoanApplicationLayout>
  );
}
