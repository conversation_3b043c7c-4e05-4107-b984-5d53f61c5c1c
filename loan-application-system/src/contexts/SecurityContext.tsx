'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { encryptSensitiveData, decryptSensitiveData, checkRateLimit } from '@/utils/validation';

interface SecurityContextType {
  isSecureSession: boolean;
  sessionTimeout: number;
  setSecureData: (key: string, data: any) => void;
  getSecureData: (key: string) => any;
  clearSecureData: () => void;
  checkSecurity: () => boolean;
  logSecurityEvent: (event: string, severity: 'low' | 'medium' | 'high') => void;
  isRateLimited: (action: string) => boolean;
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined);

export const useSecurityContext = () => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurityContext must be used within a SecurityProvider');
  }
  return context;
};

interface SecurityProviderProps {
  children: React.ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {
  const [isSecureSession, setIsSecureSession] = useState(false);
  const [sessionTimeout, setSessionTimeout] = useState(30 * 60 * 1000); // 30 minutes
  const [sessionStartTime, setSessionStartTime] = useState(Date.now());

  useEffect(() => {
    // Initialize secure session
    setIsSecureSession(true);
    setSessionStartTime(Date.now());

    // Set up session timeout
    const timeoutId = setTimeout(() => {
      clearSecureData();
      setIsSecureSession(false);
      alert('Session expired for security reasons. Please restart your application.');
    }, sessionTimeout);

    // Cleanup on unmount
    return () => {
      clearTimeout(timeoutId);
    };
  }, [sessionTimeout]);

  // Monitor for suspicious activity
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        logSecurityEvent('Page hidden - potential security risk', 'medium');
      }
    };

    const handleBeforeUnload = () => {
      clearSecureData();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const setSecureData = (key: string, data: any) => {
    try {
      const encryptedData = encryptSensitiveData({
        data,
        timestamp: Date.now(),
        sessionId: sessionStartTime
      });
      
      // Use sessionStorage for sensitive data (cleared when tab closes)
      sessionStorage.setItem(`secure_${key}`, encryptedData);
      
      logSecurityEvent(`Secure data stored: ${key}`, 'low');
    } catch (error) {
      logSecurityEvent(`Failed to store secure data: ${key}`, 'high');
      console.error('Failed to store secure data:', error);
    }
  };

  const getSecureData = (key: string) => {
    try {
      const encryptedData = sessionStorage.getItem(`secure_${key}`);
      if (!encryptedData) return null;

      const decryptedData = decryptSensitiveData(encryptedData);
      if (!decryptedData) return null;

      // Verify session integrity
      if (decryptedData.sessionId !== sessionStartTime) {
        logSecurityEvent(`Session integrity check failed for: ${key}`, 'high');
        return null;
      }

      // Check data age (expire after 1 hour)
      if (Date.now() - decryptedData.timestamp > 60 * 60 * 1000) {
        logSecurityEvent(`Expired data access attempt: ${key}`, 'medium');
        sessionStorage.removeItem(`secure_${key}`);
        return null;
      }

      return decryptedData.data;
    } catch (error) {
      logSecurityEvent(`Failed to retrieve secure data: ${key}`, 'high');
      console.error('Failed to retrieve secure data:', error);
      return null;
    }
  };

  const clearSecureData = () => {
    try {
      // Clear all secure data from sessionStorage
      const keys = Object.keys(sessionStorage);
      keys.forEach(key => {
        if (key.startsWith('secure_')) {
          sessionStorage.removeItem(key);
        }
      });
      
      // Clear rate limiting data
      const rateLimitKeys = Object.keys(localStorage);
      rateLimitKeys.forEach(key => {
        if (key.startsWith('rate_limit_')) {
          localStorage.removeItem(key);
        }
      });

      logSecurityEvent('All secure data cleared', 'low');
    } catch (error) {
      logSecurityEvent('Failed to clear secure data', 'high');
      console.error('Failed to clear secure data:', error);
    }
  };

  const checkSecurity = (): boolean => {
    // Check if session is still valid
    if (!isSecureSession) return false;

    // Check session timeout
    if (Date.now() - sessionStartTime > sessionTimeout) {
      setIsSecureSession(false);
      clearSecureData();
      return false;
    }

    // Check for suspicious activity patterns
    const securityLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    const recentHighSeverityEvents = securityLogs.filter(
      (log: any) => log.severity === 'high' && Date.now() - log.timestamp < 5 * 60 * 1000
    );

    if (recentHighSeverityEvents.length > 3) {
      logSecurityEvent('Multiple high-severity security events detected', 'high');
      setIsSecureSession(false);
      clearSecureData();
      return false;
    }

    return true;
  };

  const logSecurityEvent = (event: string, severity: 'low' | 'medium' | 'high') => {
    try {
      const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');
      const newLog = {
        event,
        severity,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };

      logs.push(newLog);

      // Keep only last 100 logs
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }

      localStorage.setItem('security_logs', JSON.stringify(logs));

      // Alert on high severity events
      if (severity === 'high') {
        console.warn('High severity security event:', event);
      }
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  };

  const isRateLimited = (action: string): boolean => {
    return !checkRateLimit(action);
  };

  const contextValue: SecurityContextType = {
    isSecureSession,
    sessionTimeout,
    setSecureData,
    getSecureData,
    clearSecureData,
    checkSecurity,
    logSecurityEvent,
    isRateLimited
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};
