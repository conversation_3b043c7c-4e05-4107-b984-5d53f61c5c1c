{"version": 3, "sources": ["../../../src/server/app-render/entry-base.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nexport {\n  createTemporaryReferenceSet,\n  renderToReadableStream,\n  decodeReply,\n  decodeAction,\n  decodeFormState,\n} from 'react-server-dom-webpack/server.edge'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport { unstable_prerender as prerender } from 'react-server-dom-webpack/static.edge'\n\nimport LayoutRouter from '../../client/components/layout-router'\nimport RenderFromTemplateContext from '../../client/components/render-from-template-context'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { actionAsyncStorage } from '../app-render/action-async-storage.external'\nimport { ClientPageRoot } from '../../client/components/client-page'\nimport { ClientSegmentRoot } from '../../client/components/client-segment'\nimport {\n  createServerSearchParamsForServerPage,\n  createPrerenderSearchParamsForClientPage,\n} from '../request/search-params'\nimport {\n  createServerParamsForServerSegment,\n  createPrerenderParamsForClientSegment,\n} from '../request/params'\nimport * as serverHooks from '../../client/components/hooks-server-context'\nimport { HTTPAccessFallbackBoundary } from '../../client/components/http-access-fallback/error-boundary'\nimport { createMetadataComponents } from '../../lib/metadata/metadata'\nimport { patchFetch as _patchFetch } from '../lib/patch-fetch'\n// not being used but needs to be included in the client manifest for /_not-found\nimport '../../client/components/error-boundary'\nimport {\n  MetadataBoundary,\n  ViewportBoundary,\n  OutletBoundary,\n} from '../../client/components/metadata/metadata-boundary'\n\nimport { preloadStyle, preloadFont, preconnect } from './rsc/preloads'\nimport { Postpone } from './rsc/postpone'\nimport { taintObjectReference } from './rsc/taint'\nexport { collectSegmentData } from './collect-segment-data'\n\n// patchFetch makes use of APIs such as `React.unstable_postpone` which are only available\n// in the experimental channel of React, so export it from here so that it comes from the bundled runtime\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  LayoutRouter,\n  RenderFromTemplateContext,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  actionAsyncStorage,\n  createServerSearchParamsForServerPage,\n  createPrerenderSearchParamsForClientPage,\n  createServerParamsForServerSegment,\n  createPrerenderParamsForClientSegment,\n  serverHooks,\n  preloadStyle,\n  preloadFont,\n  preconnect,\n  Postpone,\n  MetadataBoundary,\n  ViewportBoundary,\n  OutletBoundary,\n  taintObjectReference,\n  ClientPageRoot,\n  ClientSegmentRoot,\n  HTTPAccessFallbackBoundary,\n  patchFetch,\n  createMetadataComponents,\n}\n"], "names": ["ClientPageRoot", "ClientSegmentRoot", "HTTPAccessFallbackBoundary", "LayoutRouter", "MetadataBoundary", "OutletBoundary", "Postpone", "RenderFromTemplateContext", "ViewportBoundary", "actionAsyncStorage", "collectSegmentData", "createMetadataComponents", "createPrerenderParamsForClientSegment", "createPrerenderSearchParamsForClientPage", "createServerParamsForServerSegment", "createServerSearchParamsForServerPage", "createTemporaryReferenceSet", "decodeAction", "decodeFormState", "decodeReply", "patchFetch", "preconnect", "preloadFont", "preloadStyle", "prerender", "unstable_prerender", "renderToReadableStream", "serverHooks", "taintObjectReference", "workAsyncStorage", "workUnitAsyncStorage", "_patchFetch"], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwE3DA,cAAc;eAAdA,0BAAc;;IACdC,iBAAiB;eAAjBA,gCAAiB;;IACjBC,0BAA0B;eAA1BA,yCAA0B;;IApB1BC,YAAY;eAAZA,qBAAY;;IAcZC,gBAAgB;eAAhBA,kCAAgB;;IAEhBC,cAAc;eAAdA,gCAAc;;IAHdC,QAAQ;eAARA,kBAAQ;;IAZRC,yBAAyB;eAAzBA,kCAAyB;;IAczBC,gBAAgB;eAAhBA,kCAAgB;;IAXhBC,kBAAkB;eAAlBA,8CAAkB;;IAhBXC,kBAAkB;eAAlBA,sCAAkB;;IAkCzBC,wBAAwB;eAAxBA,kCAAwB;;IAdxBC,qCAAqC;eAArCA,6CAAqC;;IAFrCC,wCAAwC;eAAxCA,sDAAwC;;IACxCC,kCAAkC;eAAlCA,0CAAkC;;IAFlCC,qCAAqC;eAArCA,mDAAqC;;IAzDrCC,2BAA2B;eAA3BA,uCAA2B;;IAG3BC,YAAY;eAAZA,wBAAY;;IACZC,eAAe;eAAfA,2BAAe;;IAFfC,WAAW;eAAXA,uBAAW;;IAuEXC,UAAU;eAAVA;;IATAC,UAAU;eAAVA,oBAAU;;IADVC,WAAW;eAAXA,qBAAW;;IADXC,YAAY;eAAZA,sBAAY;;IAtDiBC,SAAS;eAA/BC,8BAAkB;;IAPzBC,sBAAsB;eAAtBA,kCAAsB;;IA4DtBC,WAAW;eAAXA;;IAQAC,oBAAoB;eAApBA,2BAAoB;;IAfpBC,gBAAgB;eAAhBA,0CAAgB;;IAChBC,oBAAoB;eAApBA,kDAAoB;;;4BAlDf;4BAGyC;qEAEvB;kFACa;0CACL;8CACI;4CACF;4BACJ;+BACG;8BAI3B;wBAIA;4EACsB;+BACc;0BACF;4BACC;QAEnC;kCAKA;0BAE+C;0BAC7B;uBACY;oCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,0FAA0F;AAC1F,yGAAyG;AACzG,SAASV;IACP,OAAOW,IAAAA,sBAAW,EAAC;QACjBF,kBAAAA,0CAAgB;QAChBC,sBAAAA,kDAAoB;IACtB;AACF"}