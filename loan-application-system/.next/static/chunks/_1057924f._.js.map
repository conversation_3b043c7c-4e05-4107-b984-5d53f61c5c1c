{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/src/components/SecureInput.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { ValidationResult } from '@/utils/validation';\nimport { useSecurityContext } from '@/contexts/SecurityContext';\n\ninterface SecureInputProps {\n  type: 'text' | 'tel' | 'email' | 'password' | 'number';\n  value: string;\n  onChange: (value: string) => void;\n  onValidation?: (result: ValidationResult) => void;\n  validator?: (value: string) => ValidationResult;\n  placeholder?: string;\n  label?: string;\n  required?: boolean;\n  maxLength?: number;\n  className?: string;\n  isSecure?: boolean;\n  autoComplete?: string;\n  disabled?: boolean;\n}\n\nexport const SecureInput: React.FC<SecureInputProps> = ({\n  type,\n  value,\n  onChange,\n  onValidation,\n  validator,\n  placeholder,\n  label,\n  required = false,\n  maxLength,\n  className = '',\n  isSecure = false,\n  autoComplete = 'off',\n  disabled = false\n}) => {\n  const [validationResult, setValidationResult] = useState<ValidationResult>({ isValid: true });\n  const [isFocused, setIsFocused] = useState(false);\n  const [attempts, setAttempts] = useState(0);\n  const { logSecurityEvent, isRateLimited } = useSecurityContext();\n\n  useEffect(() => {\n    if (validator && value) {\n      const result = validator(value);\n      setValidationResult(result);\n      onValidation?.(result);\n\n      if (!result.isValid && result.severity === 'high') {\n        logSecurityEvent(`Invalid input detected: ${result.error}`, 'high');\n      }\n    }\n  }, [value, validator, onValidation, logSecurityEvent]);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value;\n\n    // Rate limiting for security\n    if (isRateLimited('input_change')) {\n      logSecurityEvent('Rate limit exceeded for input changes', 'high');\n      return;\n    }\n\n    // Track failed attempts\n    if (validator) {\n      const result = validator(newValue);\n      if (!result.isValid) {\n        setAttempts(prev => prev + 1);\n        if (attempts > 5) {\n          logSecurityEvent('Multiple validation failures detected', 'medium');\n        }\n      } else {\n        setAttempts(0);\n      }\n    }\n\n    onChange(newValue);\n  };\n\n  const handleFocus = () => {\n    setIsFocused(true);\n    if (isSecure) {\n      logSecurityEvent('Secure input focused', 'low');\n    }\n  };\n\n  const handleBlur = () => {\n    setIsFocused(false);\n    if (isSecure && value) {\n      logSecurityEvent('Secure input completed', 'low');\n    }\n  };\n\n  const handlePaste = (e: React.ClipboardEvent) => {\n    if (isSecure) {\n      logSecurityEvent('Paste attempt on secure input', 'medium');\n      e.preventDefault();\n    }\n  };\n\n  const handleCopy = (e: React.ClipboardEvent) => {\n    if (isSecure) {\n      logSecurityEvent('Copy attempt on secure input', 'high');\n      e.preventDefault();\n    }\n  };\n\n  const getInputClassName = () => {\n    let baseClass = `w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 transition-all duration-200 ${className}`;\n    \n    if (isSecure) {\n      baseClass += ' secure-input';\n    }\n    \n    if (validationResult.isValid === false) {\n      baseClass += ' error-input';\n    } else if (validationResult.isValid && value) {\n      baseClass += ' success-input';\n    } else {\n      baseClass += ' border-gray-300 focus:border-orange-500';\n    }\n    \n    if (disabled) {\n      baseClass += ' bg-gray-100 cursor-not-allowed';\n    }\n    \n    return baseClass;\n  };\n\n  const getSeverityIcon = (severity?: string) => {\n    switch (severity) {\n      case 'high':\n        return '🚨';\n      case 'medium':\n        return '⚠️';\n      case 'low':\n        return 'ℹ️';\n      default:\n        return '❌';\n    }\n  };\n\n  return (\n    <div className=\"space-y-2\">\n      {label && (\n        <label className=\"block text-sm font-medium text-gray-700\">\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n          {isSecure && (\n            <span className=\"ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n              🔒 Secure\n            </span>\n          )}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <input\n          type={type}\n          value={value}\n          onChange={handleChange}\n          onFocus={handleFocus}\n          onBlur={handleBlur}\n          onPaste={handlePaste}\n          onCopy={handleCopy}\n          placeholder={placeholder}\n          required={required}\n          maxLength={maxLength}\n          autoComplete={autoComplete}\n          disabled={disabled}\n          className={getInputClassName()}\n          spellCheck={false}\n          autoCorrect=\"off\"\n          autoCapitalize=\"off\"\n        />\n        \n        {/* Validation status indicator */}\n        {value && (\n          <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n            {validationResult.isValid ? (\n              <span className=\"text-green-500 text-lg\">✓</span>\n            ) : (\n              <span className=\"text-red-500 text-lg\">✗</span>\n            )}\n          </div>\n        )}\n        \n        {/* Security indicator */}\n        {isSecure && isFocused && (\n          <div className=\"absolute -top-2 -right-2 w-4 h-4 bg-orange-500 rounded-full animate-pulse\"></div>\n        )}\n      </div>\n      \n      {/* Validation error message */}\n      {!validationResult.isValid && validationResult.error && (\n        <div className=\"error-text\">\n          <span>{getSeverityIcon(validationResult.severity)}</span>\n          <span>{validationResult.error}</span>\n        </div>\n      )}\n      \n      {/* Security warning for multiple attempts */}\n      {attempts > 3 && (\n        <div className=\"text-orange-600 text-sm flex items-center gap-2\">\n          <span>⚠️</span>\n          <span>Multiple validation attempts detected. Please verify your input.</span>\n        </div>\n      )}\n      \n      {/* Character count for secure inputs */}\n      {isSecure && maxLength && (\n        <div className=\"text-xs text-gray-500 text-right\">\n          {value.length}/{maxLength}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAsBO,MAAM,cAA0C,CAAC,EACtD,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,WAAW,EACX,KAAK,EACL,WAAW,KAAK,EAChB,SAAS,EACT,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,eAAe,KAAK,EACpB,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAAE,SAAS;IAAK;IAC3F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,gBAAgB,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,aAAa,OAAO;gBACtB,MAAM,SAAS,UAAU;gBACzB,oBAAoB;gBACpB,eAAe;gBAEf,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,QAAQ,KAAK,QAAQ;oBACjD,iBAAiB,CAAC,wBAAwB,EAAE,OAAO,KAAK,EAAE,EAAE;gBAC9D;YACF;QACF;gCAAG;QAAC;QAAO;QAAW;QAAc;KAAiB;IAErD,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAE/B,6BAA6B;QAC7B,IAAI,cAAc,iBAAiB;YACjC,iBAAiB,yCAAyC;YAC1D;QACF;QAEA,wBAAwB;QACxB,IAAI,WAAW;YACb,MAAM,SAAS,UAAU;YACzB,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,YAAY,CAAA,OAAQ,OAAO;gBAC3B,IAAI,WAAW,GAAG;oBAChB,iBAAiB,yCAAyC;gBAC5D;YACF,OAAO;gBACL,YAAY;YACd;QACF;QAEA,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,IAAI,UAAU;YACZ,iBAAiB,wBAAwB;QAC3C;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;QACb,IAAI,YAAY,OAAO;YACrB,iBAAiB,0BAA0B;QAC7C;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU;YACZ,iBAAiB,iCAAiC;YAClD,EAAE,cAAc;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU;YACZ,iBAAiB,gCAAgC;YACjD,EAAE,cAAc;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,YAAY,CAAC,kGAAkG,EAAE,WAAW;QAEhI,IAAI,UAAU;YACZ,aAAa;QACf;QAEA,IAAI,iBAAiB,OAAO,KAAK,OAAO;YACtC,aAAa;QACf,OAAO,IAAI,iBAAiB,OAAO,IAAI,OAAO;YAC5C,aAAa;QACf,OAAO;YACL,aAAa;QACf;QAEA,IAAI,UAAU;YACZ,aAAa;QACf;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;oBAChD,0BACC,6LAAC;wBAAK,WAAU;kCAA+D;;;;;;;;;;;;0BAOrF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAM;wBACN,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,QAAQ;wBACR,SAAS;wBACT,QAAQ;wBACR,aAAa;wBACb,UAAU;wBACV,WAAW;wBACX,cAAc;wBACd,UAAU;wBACV,WAAW;wBACX,YAAY;wBACZ,aAAY;wBACZ,gBAAe;;;;;;oBAIhB,uBACC,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,OAAO,iBACvB,6LAAC;4BAAK,WAAU;sCAAyB;;;;;iDAEzC,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;oBAM5C,YAAY,2BACX,6LAAC;wBAAI,WAAU;;;;;;;;;;;;YAKlB,CAAC,iBAAiB,OAAO,IAAI,iBAAiB,KAAK,kBAClD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAM,gBAAgB,iBAAiB,QAAQ;;;;;;kCAChD,6LAAC;kCAAM,iBAAiB,KAAK;;;;;;;;;;;;YAKhC,WAAW,mBACV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAK;;;;;;kCACN,6LAAC;kCAAK;;;;;;;;;;;;YAKT,YAAY,2BACX,6LAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM;oBAAC;oBAAE;;;;;;;;;;;;;AAK1B;GAnMa;;QAkBiC,sIAAA,CAAA,qBAAkB;;;KAlBnD", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/src/app/loan-application/pan/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { SecureInput } from \"@/components/SecureInput\";\nimport { validatePAN, validateName, ValidationResult } from \"@/utils/validation\";\nimport { useSecurityContext } from \"@/contexts/SecurityContext\";\nimport { LoanApplicationWrapper } from \"@/components/LoanApplicationWrapper\";\n\nfunction PANVerification() {\n  const [panNumber, setPanNumber] = useState(\"\");\n  const [fullName, setFullName] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [panValidation, setPanValidation] = useState<ValidationResult>({ isValid: true });\n  const [nameValidation, setNameValidation] = useState<ValidationResult>({ isValid: true });\n  const router = useRouter();\n  const { setSecureData, logSecurityEvent, isRateLimited } = useSecurityContext();\n\n  const formatPAN = (value: string) => {\n    // Remove all non-alphanumeric characters and convert to uppercase\n    const cleaned = value.replace(/[^A-Z0-9]/gi, '').toUpperCase();\n    \n    // Format as **********\n    if (cleaned.length <= 5) {\n      return cleaned;\n    } else if (cleaned.length <= 9) {\n      return cleaned.slice(0, 5) + cleaned.slice(5);\n    } else {\n      return cleaned.slice(0, 5) + cleaned.slice(5, 9) + cleaned.slice(9, 10);\n    }\n  };\n\n  const isValidPAN = (pan: string) => {\n    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;\n    return panRegex.test(pan);\n  };\n\n  const handlePANChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const formatted = formatPAN(e.target.value);\n    if (formatted.length <= 10) {\n      setPanNumber(formatted);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Rate limiting check\n    if (isRateLimited('pan_submit')) {\n      logSecurityEvent('PAN submission rate limit exceeded', 'high');\n      alert('Too many attempts. Please wait before trying again.');\n      return;\n    }\n\n    // Validate PAN and name\n    const panValidationResult = validatePAN(panNumber);\n    const nameValidationResult = validateName(fullName);\n\n    if (!panValidationResult.isValid || !nameValidationResult.isValid) {\n      setPanValidation(panValidationResult);\n      setNameValidation(nameValidationResult);\n      logSecurityEvent('Invalid PAN or name submission attempt', 'medium');\n      return;\n    }\n\n    setIsLoading(true);\n    logSecurityEvent('PAN verification initiated', 'low');\n\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Store PAN data securely\n    setSecureData(\"panNumber\", panNumber);\n    setSecureData(\"fullName\", fullName.trim());\n\n    router.push(\"/loan-application/pan-confirm\");\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm px-4 py-3 flex items-center justify-between\">\n        <button\n          type=\"button\"\n          onClick={() => router.back()}\n          className=\"w-8 h-8 flex items-center justify-center\"\n        >\n          <span className=\"text-gray-600\">←</span>\n        </button>\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center\">\n            <span className=\"text-white font-bold text-sm\">N</span>\n          </div>\n          <span className=\"text-orange-500 font-bold text-lg\">Nira</span>\n        </div>\n        <div className=\"w-8 h-8\"></div>\n      </header>\n\n      {/* Progress Bar */}\n      <div className=\"bg-white px-4 py-2\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <span className=\"text-sm text-gray-600\">Step 3 of 5</span>\n          <span className=\"text-sm text-gray-600\">60%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div className=\"bg-orange-500 h-2 rounded-full w-3/5\"></div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <main className=\"flex-1 px-4 py-6\">\n        <div className=\"max-w-md mx-auto\">\n          <h1 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Is this your PAN number?\n          </h1>\n          <p className=\"text-gray-600 mb-8\">\n            Please enter your PAN details as per your PAN card\n          </p>\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <SecureInput\n              type=\"text\"\n              value={panNumber}\n              onChange={(value) => setPanNumber(formatPAN(value))}\n              onValidation={setPanValidation}\n              validator={validatePAN}\n              placeholder=\"**********\"\n              label=\"PAN Number\"\n              required\n              maxLength={10}\n              isSecure={true}\n              className=\"font-mono tracking-wider\"\n              autoComplete=\"off\"\n            />\n\n            <SecureInput\n              type=\"text\"\n              value={fullName}\n              onChange={setFullName}\n              onValidation={setNameValidation}\n              validator={validateName}\n              placeholder=\"Enter your full name\"\n              label=\"Full Name (as per PAN)\"\n              required\n              maxLength={100}\n              isSecure={true}\n              autoComplete=\"name\"\n            />\n\n            <button\n              type=\"submit\"\n              disabled={!panValidation.isValid || !nameValidation.isValid || !panNumber || !fullName.trim() || isLoading}\n              className=\"w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-orange-600 transition-colors\"\n            >\n              {isLoading ? \"VERIFYING...\" : \"VERIFY DETAILS\"}\n            </button>\n          </form>\n\n          <div className=\"mt-8 space-y-3\">\n            <div className=\"flex items-center gap-3 text-sm text-gray-600\">\n              <span className=\"w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-blue-600 text-xs\">🔒</span>\n              </span>\n              <span>Your PAN details are encrypted and secure</span>\n            </div>\n            <div className=\"flex items-center gap-3 text-sm text-gray-600\">\n              <span className=\"w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-blue-600 text-xs\">✓</span>\n              </span>\n              <span>We verify your details with government databases</span>\n            </div>\n          </div>\n\n          <div className=\"mt-6 bg-yellow-50 rounded-lg p-4\">\n            <div className=\"flex items-start gap-3\">\n              <span className=\"text-yellow-600 text-sm\">⚠️</span>\n              <div className=\"text-sm text-yellow-800\">\n                <p className=\"font-medium mb-1\">Important Note</p>\n                <p>Please ensure your PAN details match exactly with your PAN card to avoid verification delays.</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASA,SAAS;;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAAE,SAAS;IAAK;IACrF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAAE,SAAS;IAAK;IACvF,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD;IAE5E,MAAM,YAAY,CAAC;QACjB,kEAAkE;QAClE,MAAM,UAAU,MAAM,OAAO,CAAC,eAAe,IAAI,WAAW;QAE5D,uBAAuB;QACvB,IAAI,QAAQ,MAAM,IAAI,GAAG;YACvB,OAAO;QACT,OAAO,IAAI,QAAQ,MAAM,IAAI,GAAG;YAC9B,OAAO,QAAQ,KAAK,CAAC,GAAG,KAAK,QAAQ,KAAK,CAAC;QAC7C,OAAO;YACL,OAAO,QAAQ,KAAK,CAAC,GAAG,KAAK,QAAQ,KAAK,CAAC,GAAG,KAAK,QAAQ,KAAK,CAAC,GAAG;QACtE;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,WAAW;QACjB,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,YAAY,UAAU,EAAE,MAAM,CAAC,KAAK;QAC1C,IAAI,UAAU,MAAM,IAAI,IAAI;YAC1B,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,sBAAsB;QACtB,IAAI,cAAc,eAAe;YAC/B,iBAAiB,sCAAsC;YACvD,MAAM;YACN;QACF;QAEA,wBAAwB;QACxB,MAAM,sBAAsB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE;QACxC,MAAM,uBAAuB,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;QAE1C,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,qBAAqB,OAAO,EAAE;YACjE,iBAAiB;YACjB,kBAAkB;YAClB,iBAAiB,0CAA0C;YAC3D;QACF;QAEA,aAAa;QACb,iBAAiB,8BAA8B;QAE/C,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,0BAA0B;QAC1B,cAAc,aAAa;QAC3B,cAAc,YAAY,SAAS,IAAI;QAEvC,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCAEV,cAAA,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;kCAElC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;0CAEjD,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAE1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAIlC,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC,oIAAA,CAAA,cAAW;oCACV,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,QAAU,aAAa,UAAU;oCAC5C,cAAc;oCACd,WAAW,6HAAA,CAAA,cAAW;oCACtB,aAAY;oCACZ,OAAM;oCACN,QAAQ;oCACR,WAAW;oCACX,UAAU;oCACV,WAAU;oCACV,cAAa;;;;;;8CAGf,6LAAC,oIAAA,CAAA,cAAW;oCACV,MAAK;oCACL,OAAO;oCACP,UAAU;oCACV,cAAc;oCACd,WAAW,6HAAA,CAAA,eAAY;oCACvB,aAAY;oCACZ,OAAM;oCACN,QAAQ;oCACR,WAAW;oCACX,UAAU;oCACV,cAAa;;;;;;8CAGf,6LAAC;oCACC,MAAK;oCACL,UAAU,CAAC,cAAc,OAAO,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,MAAM;oCACjG,WAAU;8CAET,YAAY,iBAAiB;;;;;;;;;;;;sCAIlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACd,cAAA,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;sDAE1C,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACd,cAAA,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;sDAE1C,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAIV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA0B;;;;;;kDAC1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAmB;;;;;;0DAChC,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GAjLS;;QAMQ,qIAAA,CAAA,YAAS;QACmC,sIAAA,CAAA,qBAAkB;;;KAPtE", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}