{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/src/utils/validation.ts"], "sourcesContent": ["// Security validation utilities for loan application\n\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n  severity?: 'low' | 'medium' | 'high';\n}\n\n// Phone number validation\nexport const validatePhoneNumber = (phone: string): ValidationResult => {\n  // Remove all non-digits\n  const cleanPhone = phone.replace(/\\D/g, '');\n  \n  if (!cleanPhone) {\n    return { isValid: false, error: 'Phone number is required', severity: 'high' };\n  }\n  \n  if (cleanPhone.length !== 10) {\n    return { isValid: false, error: 'Phone number must be 10 digits', severity: 'high' };\n  }\n  \n  // Check for invalid patterns\n  if (/^[0-5]/.test(cleanPhone)) {\n    return { isValid: false, error: 'Invalid phone number format', severity: 'medium' };\n  }\n  \n  // Check for repeated digits (security concern)\n  if (/^(\\d)\\1{9}$/.test(cleanPhone)) {\n    return { isValid: false, error: 'Phone number cannot have all same digits', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// OTP validation\nexport const validateOTP = (otp: string): ValidationResult => {\n  const cleanOTP = otp.replace(/\\D/g, '');\n  \n  if (!cleanOTP) {\n    return { isValid: false, error: 'OTP is required', severity: 'high' };\n  }\n  \n  if (cleanOTP.length !== 6) {\n    return { isValid: false, error: 'OTP must be 6 digits', severity: 'high' };\n  }\n  \n  // Check for sequential numbers (security concern)\n  if (/123456|654321|111111|000000/.test(cleanOTP)) {\n    return { isValid: false, error: 'Invalid OTP pattern', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// PAN validation with enhanced security\nexport const validatePAN = (pan: string): ValidationResult => {\n  const cleanPAN = pan.replace(/[^A-Z0-9]/g, '').toUpperCase();\n  \n  if (!cleanPAN) {\n    return { isValid: false, error: 'PAN is required', severity: 'high' };\n  }\n  \n  if (cleanPAN.length !== 10) {\n    return { isValid: false, error: 'PAN must be 10 characters', severity: 'high' };\n  }\n  \n  // PAN format: **********\n  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;\n  if (!panRegex.test(cleanPAN)) {\n    return { isValid: false, error: 'Invalid PAN format (**********)', severity: 'high' };\n  }\n  \n  // Check for invalid patterns\n  if (/AAAAA|11111|00000/.test(cleanPAN)) {\n    return { isValid: false, error: 'Invalid PAN pattern detected', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// Name validation\nexport const validateName = (name: string): ValidationResult => {\n  const cleanName = name.trim();\n  \n  if (!cleanName) {\n    return { isValid: false, error: 'Name is required', severity: 'high' };\n  }\n  \n  if (cleanName.length < 2) {\n    return { isValid: false, error: 'Name must be at least 2 characters', severity: 'medium' };\n  }\n  \n  if (cleanName.length > 100) {\n    return { isValid: false, error: 'Name cannot exceed 100 characters', severity: 'medium' };\n  }\n  \n  // Only allow letters, spaces, dots, and hyphens\n  const nameRegex = /^[a-zA-Z\\s.-]+$/;\n  if (!nameRegex.test(cleanName)) {\n    return { isValid: false, error: 'Name can only contain letters, spaces, dots, and hyphens', severity: 'medium' };\n  }\n  \n  // Check for suspicious patterns\n  if (/(.)\\1{4,}/.test(cleanName.replace(/\\s/g, ''))) {\n    return { isValid: false, error: 'Invalid name pattern detected', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// Income validation\nexport const validateIncome = (income: string): ValidationResult => {\n  const cleanIncome = income.replace(/[^\\d]/g, '');\n  const incomeNumber = parseInt(cleanIncome);\n  \n  if (!cleanIncome) {\n    return { isValid: false, error: 'Income is required', severity: 'high' };\n  }\n  \n  if (incomeNumber < 10000) {\n    return { isValid: false, error: 'Minimum income should be ₹10,000', severity: 'medium' };\n  }\n  \n  if (incomeNumber > 10000000) {\n    return { isValid: false, error: 'Income seems unusually high', severity: 'medium' };\n  }\n  \n  return { isValid: true };\n};\n\n// Address validation\nexport const validateAddress = (address: string): ValidationResult => {\n  const cleanAddress = address.trim();\n  \n  if (!cleanAddress) {\n    return { isValid: false, error: 'Address is required', severity: 'high' };\n  }\n  \n  if (cleanAddress.length < 10) {\n    return { isValid: false, error: 'Address must be at least 10 characters', severity: 'medium' };\n  }\n  \n  if (cleanAddress.length > 200) {\n    return { isValid: false, error: 'Address cannot exceed 200 characters', severity: 'medium' };\n  }\n  \n  // Check for suspicious patterns\n  if (/(.)\\1{10,}/.test(cleanAddress.replace(/\\s/g, ''))) {\n    return { isValid: false, error: 'Invalid address pattern detected', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// Pincode validation\nexport const validatePincode = (pincode: string): ValidationResult => {\n  const cleanPincode = pincode.replace(/\\D/g, '');\n  \n  if (!cleanPincode) {\n    return { isValid: false, error: 'Pincode is required', severity: 'high' };\n  }\n  \n  if (cleanPincode.length !== 6) {\n    return { isValid: false, error: 'Pincode must be 6 digits', severity: 'high' };\n  }\n  \n  // Check for invalid patterns\n  if (/^[0-1]/.test(cleanPincode)) {\n    return { isValid: false, error: 'Invalid pincode format', severity: 'medium' };\n  }\n  \n  if (/^(\\d)\\1{5}$/.test(cleanPincode)) {\n    return { isValid: false, error: 'Pincode cannot have all same digits', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// Company name validation\nexport const validateCompanyName = (company: string): ValidationResult => {\n  const cleanCompany = company.trim();\n  \n  if (!cleanCompany) {\n    return { isValid: false, error: 'Company name is required', severity: 'high' };\n  }\n  \n  if (cleanCompany.length < 2) {\n    return { isValid: false, error: 'Company name must be at least 2 characters', severity: 'medium' };\n  }\n  \n  if (cleanCompany.length > 100) {\n    return { isValid: false, error: 'Company name cannot exceed 100 characters', severity: 'medium' };\n  }\n  \n  return { isValid: true };\n};\n\n// Data sanitization\nexport const sanitizeInput = (input: string): string => {\n  return input\n    .trim()\n    .replace(/[<>]/g, '') // Remove potential HTML tags\n    .replace(/['\"]/g, '') // Remove quotes\n    .replace(/\\s+/g, ' '); // Normalize whitespace\n};\n\n// Rate limiting check (simple implementation)\nexport const checkRateLimit = (key: string, maxAttempts: number = 5, windowMs: number = 300000): boolean => {\n  const now = Date.now();\n  const attempts = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || '[]');\n  \n  // Filter attempts within the time window\n  const recentAttempts = attempts.filter((timestamp: number) => now - timestamp < windowMs);\n  \n  if (recentAttempts.length >= maxAttempts) {\n    return false; // Rate limit exceeded\n  }\n  \n  // Add current attempt\n  recentAttempts.push(now);\n  localStorage.setItem(`rate_limit_${key}`, JSON.stringify(recentAttempts));\n  \n  return true;\n};\n\n// Encrypt sensitive data before storing\nexport const encryptSensitiveData = (data: any): string => {\n  // Simple base64 encoding for demo (use proper encryption in production)\n  return btoa(JSON.stringify(data));\n};\n\n// Decrypt sensitive data\nexport const decryptSensitiveData = (encryptedData: string): any => {\n  try {\n    return JSON.parse(atob(encryptedData));\n  } catch {\n    return null;\n  }\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;AAS9C,MAAM,sBAAsB,CAAC;IAClC,wBAAwB;IACxB,MAAM,aAAa,MAAM,OAAO,CAAC,OAAO;IAExC,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4B,UAAU;QAAO;IAC/E;IAEA,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;YAAkC,UAAU;QAAO;IACrF;IAEA,6BAA6B;IAC7B,IAAI,SAAS,IAAI,CAAC,aAAa;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA+B,UAAU;QAAS;IACpF;IAEA,+CAA+C;IAC/C,IAAI,cAAc,IAAI,CAAC,aAAa;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4C,UAAU;QAAO;IAC/F;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,cAAc,CAAC;IAC1B,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;IAEpC,IAAI,CAAC,UAAU;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;YAAmB,UAAU;QAAO;IACtE;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAwB,UAAU;QAAO;IAC3E;IAEA,kDAAkD;IAClD,IAAI,8BAA8B,IAAI,CAAC,WAAW;QAChD,OAAO;YAAE,SAAS;YAAO,OAAO;YAAuB,UAAU;QAAO;IAC1E;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,cAAc,CAAC;IAC1B,MAAM,WAAW,IAAI,OAAO,CAAC,cAAc,IAAI,WAAW;IAE1D,IAAI,CAAC,UAAU;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;YAAmB,UAAU;QAAO;IACtE;IAEA,IAAI,SAAS,MAAM,KAAK,IAAI;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA6B,UAAU;QAAO;IAChF;IAEA,yBAAyB;IACzB,MAAM,WAAW;IACjB,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;YAAmC,UAAU;QAAO;IACtF;IAEA,6BAA6B;IAC7B,IAAI,oBAAoB,IAAI,CAAC,WAAW;QACtC,OAAO;YAAE,SAAS;YAAO,OAAO;YAAgC,UAAU;QAAO;IACnF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,YAAY,KAAK,IAAI;IAE3B,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;YAAoB,UAAU;QAAO;IACvE;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAsC,UAAU;QAAS;IAC3F;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;YAAqC,UAAU;QAAS;IAC1F;IAEA,gDAAgD;IAChD,MAAM,YAAY;IAClB,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4D,UAAU;QAAS;IACjH;IAEA,gCAAgC;IAChC,IAAI,YAAY,IAAI,CAAC,UAAU,OAAO,CAAC,OAAO,MAAM;QAClD,OAAO;YAAE,SAAS;YAAO,OAAO;YAAiC,UAAU;QAAO;IACpF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,cAAc,OAAO,OAAO,CAAC,UAAU;IAC7C,MAAM,eAAe,SAAS;IAE9B,IAAI,CAAC,aAAa;QAChB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAsB,UAAU;QAAO;IACzE;IAEA,IAAI,eAAe,OAAO;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAoC,UAAU;QAAS;IACzF;IAEA,IAAI,eAAe,UAAU;QAC3B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA+B,UAAU;QAAS;IACpF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,eAAe,QAAQ,IAAI;IAEjC,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAuB,UAAU;QAAO;IAC1E;IAEA,IAAI,aAAa,MAAM,GAAG,IAAI;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA0C,UAAU;QAAS;IAC/F;IAEA,IAAI,aAAa,MAAM,GAAG,KAAK;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;YAAwC,UAAU;QAAS;IAC7F;IAEA,gCAAgC;IAChC,IAAI,aAAa,IAAI,CAAC,aAAa,OAAO,CAAC,OAAO,MAAM;QACtD,OAAO;YAAE,SAAS;YAAO,OAAO;YAAoC,UAAU;QAAO;IACvF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,eAAe,QAAQ,OAAO,CAAC,OAAO;IAE5C,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAuB,UAAU;QAAO;IAC1E;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4B,UAAU;QAAO;IAC/E;IAEA,6BAA6B;IAC7B,IAAI,SAAS,IAAI,CAAC,eAAe;QAC/B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA0B,UAAU;QAAS;IAC/E;IAEA,IAAI,cAAc,IAAI,CAAC,eAAe;QACpC,OAAO;YAAE,SAAS;YAAO,OAAO;YAAuC,UAAU;QAAO;IAC1F;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI;IAEjC,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4B,UAAU;QAAO;IAC/E;IAEA,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA8C,UAAU;QAAS;IACnG;IAEA,IAAI,aAAa,MAAM,GAAG,KAAK;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA6C,UAAU;QAAS;IAClG;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,SAAS,IAAI,6BAA6B;KAClD,OAAO,CAAC,SAAS,IAAI,gBAAgB;KACrC,OAAO,CAAC,QAAQ,MAAM,uBAAuB;AAClD;AAGO,MAAM,iBAAiB,CAAC,KAAa,cAAsB,CAAC,EAAE,WAAmB,MAAM;IAC5F,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK;IAEzE,yCAAyC;IACzC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,YAAsB,MAAM,YAAY;IAEhF,IAAI,eAAe,MAAM,IAAI,aAAa;QACxC,OAAO,OAAO,sBAAsB;IACtC;IAEA,sBAAsB;IACtB,eAAe,IAAI,CAAC;IACpB,aAAa,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,SAAS,CAAC;IAEzD,OAAO;AACT;AAGO,MAAM,uBAAuB,CAAC;IACnC,wEAAwE;IACxE,OAAO,KAAK,KAAK,SAAS,CAAC;AAC7B;AAGO,MAAM,uBAAuB,CAAC;IACnC,IAAI;QACF,OAAO,KAAK,KAAK,CAAC,KAAK;IACzB,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/src/contexts/SecurityContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { encryptSensitiveData, decryptSensitiveData, checkRateLimit } from '@/utils/validation';\n\ninterface SecurityContextType {\n  isSecureSession: boolean;\n  sessionTimeout: number;\n  setSecureData: (key: string, data: any) => void;\n  getSecureData: (key: string) => any;\n  clearSecureData: () => void;\n  checkSecurity: () => boolean;\n  logSecurityEvent: (event: string, severity: 'low' | 'medium' | 'high') => void;\n  isRateLimited: (action: string) => boolean;\n}\n\nconst SecurityContext = createContext<SecurityContextType | undefined>(undefined);\n\nexport const useSecurityContext = () => {\n  const context = useContext(SecurityContext);\n  if (!context) {\n    throw new Error('useSecurityContext must be used within a SecurityProvider');\n  }\n  return context;\n};\n\ninterface SecurityProviderProps {\n  children: React.ReactNode;\n}\n\nexport const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {\n  const [isSecureSession, setIsSecureSession] = useState(false);\n  const [sessionTimeout, setSessionTimeout] = useState(30 * 60 * 1000); // 30 minutes\n  const [sessionStartTime, setSessionStartTime] = useState(Date.now());\n\n  useEffect(() => {\n    // Initialize secure session\n    setIsSecureSession(true);\n    setSessionStartTime(Date.now());\n\n    // Set up session timeout\n    const timeoutId = setTimeout(() => {\n      clearSecureData();\n      setIsSecureSession(false);\n      alert('Session expired for security reasons. Please restart your application.');\n    }, sessionTimeout);\n\n    // Cleanup on unmount\n    return () => {\n      clearTimeout(timeoutId);\n    };\n  }, [sessionTimeout]);\n\n  // Monitor for suspicious activity\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden) {\n        logSecurityEvent('Page hidden - potential security risk', 'medium');\n      }\n    };\n\n    const handleBeforeUnload = () => {\n      clearSecureData();\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    return () => {\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, []);\n\n  const setSecureData = (key: string, data: any) => {\n    try {\n      const encryptedData = encryptSensitiveData({\n        data,\n        timestamp: Date.now(),\n        sessionId: sessionStartTime\n      });\n      \n      // Use sessionStorage for sensitive data (cleared when tab closes)\n      sessionStorage.setItem(`secure_${key}`, encryptedData);\n      \n      logSecurityEvent(`Secure data stored: ${key}`, 'low');\n    } catch (error) {\n      logSecurityEvent(`Failed to store secure data: ${key}`, 'high');\n      console.error('Failed to store secure data:', error);\n    }\n  };\n\n  const getSecureData = (key: string) => {\n    try {\n      const encryptedData = sessionStorage.getItem(`secure_${key}`);\n      if (!encryptedData) return null;\n\n      const decryptedData = decryptSensitiveData(encryptedData);\n      if (!decryptedData) return null;\n\n      // Verify session integrity\n      if (decryptedData.sessionId !== sessionStartTime) {\n        logSecurityEvent(`Session integrity check failed for: ${key}`, 'high');\n        return null;\n      }\n\n      // Check data age (expire after 1 hour)\n      if (Date.now() - decryptedData.timestamp > 60 * 60 * 1000) {\n        logSecurityEvent(`Expired data access attempt: ${key}`, 'medium');\n        sessionStorage.removeItem(`secure_${key}`);\n        return null;\n      }\n\n      return decryptedData.data;\n    } catch (error) {\n      logSecurityEvent(`Failed to retrieve secure data: ${key}`, 'high');\n      console.error('Failed to retrieve secure data:', error);\n      return null;\n    }\n  };\n\n  const clearSecureData = () => {\n    try {\n      // Clear all secure data from sessionStorage\n      const keys = Object.keys(sessionStorage);\n      keys.forEach(key => {\n        if (key.startsWith('secure_')) {\n          sessionStorage.removeItem(key);\n        }\n      });\n      \n      // Clear rate limiting data\n      const rateLimitKeys = Object.keys(localStorage);\n      rateLimitKeys.forEach(key => {\n        if (key.startsWith('rate_limit_')) {\n          localStorage.removeItem(key);\n        }\n      });\n\n      logSecurityEvent('All secure data cleared', 'low');\n    } catch (error) {\n      logSecurityEvent('Failed to clear secure data', 'high');\n      console.error('Failed to clear secure data:', error);\n    }\n  };\n\n  const checkSecurity = (): boolean => {\n    // Check if session is still valid\n    if (!isSecureSession) return false;\n\n    // Check session timeout\n    if (Date.now() - sessionStartTime > sessionTimeout) {\n      setIsSecureSession(false);\n      clearSecureData();\n      return false;\n    }\n\n    // Check for suspicious activity patterns\n    const securityLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');\n    const recentHighSeverityEvents = securityLogs.filter(\n      (log: any) => log.severity === 'high' && Date.now() - log.timestamp < 5 * 60 * 1000\n    );\n\n    if (recentHighSeverityEvents.length > 3) {\n      logSecurityEvent('Multiple high-severity security events detected', 'high');\n      setIsSecureSession(false);\n      clearSecureData();\n      return false;\n    }\n\n    return true;\n  };\n\n  const logSecurityEvent = (event: string, severity: 'low' | 'medium' | 'high') => {\n    try {\n      const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');\n      const newLog = {\n        event,\n        severity,\n        timestamp: Date.now(),\n        userAgent: navigator.userAgent,\n        url: window.location.href\n      };\n\n      logs.push(newLog);\n\n      // Keep only last 100 logs\n      if (logs.length > 100) {\n        logs.splice(0, logs.length - 100);\n      }\n\n      localStorage.setItem('security_logs', JSON.stringify(logs));\n\n      // Alert on high severity events\n      if (severity === 'high') {\n        console.warn('High severity security event:', event);\n      }\n    } catch (error) {\n      console.error('Failed to log security event:', error);\n    }\n  };\n\n  const isRateLimited = (action: string): boolean => {\n    return !checkRateLimit(action);\n  };\n\n  const contextValue: SecurityContextType = {\n    isSecureSession,\n    sessionTimeout,\n    setSecureData,\n    getSecureData,\n    clearSecureData,\n    checkSecurity,\n    logSecurityEvent,\n    isRateLimited\n  };\n\n  return (\n    <SecurityContext.Provider value={contextValue}>\n      {children}\n    </SecurityContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAgBA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,MAAM,qBAAqB;;IAChC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,mBAAoD,CAAC,EAAE,QAAQ,EAAE;;IAC5E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,OAAO,aAAa;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,4BAA4B;YAC5B,mBAAmB;YACnB,oBAAoB,KAAK,GAAG;YAE5B,yBAAyB;YACzB,MAAM,YAAY;wDAAW;oBAC3B;oBACA,mBAAmB;oBACnB,MAAM;gBACR;uDAAG;YAEH,qBAAqB;YACrB;8CAAO;oBACL,aAAa;gBACf;;QACF;qCAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;qEAAyB;oBAC7B,IAAI,SAAS,MAAM,EAAE;wBACnB,iBAAiB,yCAAyC;oBAC5D;gBACF;;YAEA,MAAM;iEAAqB;oBACzB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,oBAAoB;YAC9C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC;8CAAO;oBACL,SAAS,mBAAmB,CAAC,oBAAoB;oBACjD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;qCAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC,KAAa;QAClC,IAAI;YACF,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzC;gBACA,WAAW,KAAK,GAAG;gBACnB,WAAW;YACb;YAEA,kEAAkE;YAClE,eAAe,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;YAExC,iBAAiB,CAAC,oBAAoB,EAAE,KAAK,EAAE;QACjD,EAAE,OAAO,OAAO;YACd,iBAAiB,CAAC,6BAA6B,EAAE,KAAK,EAAE;YACxD,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI;YACF,MAAM,gBAAgB,eAAe,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK;YAC5D,IAAI,CAAC,eAAe,OAAO;YAE3B,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE;YAC3C,IAAI,CAAC,eAAe,OAAO;YAE3B,2BAA2B;YAC3B,IAAI,cAAc,SAAS,KAAK,kBAAkB;gBAChD,iBAAiB,CAAC,oCAAoC,EAAE,KAAK,EAAE;gBAC/D,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,KAAK,GAAG,KAAK,cAAc,SAAS,GAAG,KAAK,KAAK,MAAM;gBACzD,iBAAiB,CAAC,6BAA6B,EAAE,KAAK,EAAE;gBACxD,eAAe,UAAU,CAAC,CAAC,OAAO,EAAE,KAAK;gBACzC,OAAO;YACT;YAEA,OAAO,cAAc,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,iBAAiB,CAAC,gCAAgC,EAAE,KAAK,EAAE;YAC3D,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,4CAA4C;YAC5C,MAAM,OAAO,OAAO,IAAI,CAAC;YACzB,KAAK,OAAO,CAAC,CAAA;gBACX,IAAI,IAAI,UAAU,CAAC,YAAY;oBAC7B,eAAe,UAAU,CAAC;gBAC5B;YACF;YAEA,2BAA2B;YAC3B,MAAM,gBAAgB,OAAO,IAAI,CAAC;YAClC,cAAc,OAAO,CAAC,CAAA;gBACpB,IAAI,IAAI,UAAU,CAAC,gBAAgB;oBACjC,aAAa,UAAU,CAAC;gBAC1B;YACF;YAEA,iBAAiB,2BAA2B;QAC9C,EAAE,OAAO,OAAO;YACd,iBAAiB,+BAA+B;YAChD,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB;QACpB,kCAAkC;QAClC,IAAI,CAAC,iBAAiB,OAAO;QAE7B,wBAAwB;QACxB,IAAI,KAAK,GAAG,KAAK,mBAAmB,gBAAgB;YAClD,mBAAmB;YACnB;YACA,OAAO;QACT;QAEA,yCAAyC;QACzC,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,2BAA2B,aAAa,MAAM,CAClD,CAAC,MAAa,IAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,KAAK,IAAI,SAAS,GAAG,IAAI,KAAK;QAGjF,IAAI,yBAAyB,MAAM,GAAG,GAAG;YACvC,iBAAiB,mDAAmD;YACpE,mBAAmB;YACnB;YACA,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC,OAAe;QACvC,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;YACjE,MAAM,SAAS;gBACb;gBACA;gBACA,WAAW,KAAK,GAAG;gBACnB,WAAW,UAAU,SAAS;gBAC9B,KAAK,OAAO,QAAQ,CAAC,IAAI;YAC3B;YAEA,KAAK,IAAI,CAAC;YAEV,0BAA0B;YAC1B,IAAI,KAAK,MAAM,GAAG,KAAK;gBACrB,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,GAAG;YAC/B;YAEA,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,gCAAgC;YAChC,IAAI,aAAa,QAAQ;gBACvB,QAAQ,IAAI,CAAC,iCAAiC;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,CAAC,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACzB;IAEA,MAAM,eAAoC;QACxC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;IAhMa;KAAA", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}