(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/utils/validation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Security validation utilities for loan application
__turbopack_context__.s({
    "checkRateLimit": (()=>checkRateLimit),
    "decryptSensitiveData": (()=>decryptSensitiveData),
    "encryptSensitiveData": (()=>encryptSensitiveData),
    "sanitizeInput": (()=>sanitizeInput),
    "validateAddress": (()=>validateAddress),
    "validateCompanyName": (()=>validateCompanyName),
    "validateIncome": (()=>validateIncome),
    "validateName": (()=>validateName),
    "validateOTP": (()=>validateOTP),
    "validatePAN": (()=>validatePAN),
    "validatePhoneNumber": (()=>validatePhoneNumber),
    "validatePincode": (()=>validatePincode)
});
const validatePhoneNumber = (phone)=>{
    // Remove all non-digits
    const cleanPhone = phone.replace(/\D/g, '');
    if (!cleanPhone) {
        return {
            isValid: false,
            error: 'Phone number is required',
            severity: 'high'
        };
    }
    if (cleanPhone.length !== 10) {
        return {
            isValid: false,
            error: 'Phone number must be 10 digits',
            severity: 'high'
        };
    }
    // Check for invalid patterns
    if (/^[0-5]/.test(cleanPhone)) {
        return {
            isValid: false,
            error: 'Invalid phone number format',
            severity: 'medium'
        };
    }
    // Check for repeated digits (security concern)
    if (/^(\d)\1{9}$/.test(cleanPhone)) {
        return {
            isValid: false,
            error: 'Phone number cannot have all same digits',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validateOTP = (otp)=>{
    const cleanOTP = otp.replace(/\D/g, '');
    if (!cleanOTP) {
        return {
            isValid: false,
            error: 'OTP is required',
            severity: 'high'
        };
    }
    if (cleanOTP.length !== 6) {
        return {
            isValid: false,
            error: 'OTP must be 6 digits',
            severity: 'high'
        };
    }
    // Check for sequential numbers (security concern)
    if (/123456|654321|111111|000000/.test(cleanOTP)) {
        return {
            isValid: false,
            error: 'Invalid OTP pattern',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validatePAN = (pan)=>{
    const cleanPAN = pan.replace(/[^A-Z0-9]/g, '').toUpperCase();
    if (!cleanPAN) {
        return {
            isValid: false,
            error: 'PAN is required',
            severity: 'high'
        };
    }
    if (cleanPAN.length !== 10) {
        return {
            isValid: false,
            error: 'PAN must be 10 characters',
            severity: 'high'
        };
    }
    // PAN format: **********
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    if (!panRegex.test(cleanPAN)) {
        return {
            isValid: false,
            error: 'Invalid PAN format (**********)',
            severity: 'high'
        };
    }
    // Check for invalid patterns
    if (/AAAAA|11111|00000/.test(cleanPAN)) {
        return {
            isValid: false,
            error: 'Invalid PAN pattern detected',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validateName = (name)=>{
    const cleanName = name.trim();
    if (!cleanName) {
        return {
            isValid: false,
            error: 'Name is required',
            severity: 'high'
        };
    }
    if (cleanName.length < 2) {
        return {
            isValid: false,
            error: 'Name must be at least 2 characters',
            severity: 'medium'
        };
    }
    if (cleanName.length > 100) {
        return {
            isValid: false,
            error: 'Name cannot exceed 100 characters',
            severity: 'medium'
        };
    }
    // Only allow letters, spaces, dots, and hyphens
    const nameRegex = /^[a-zA-Z\s.-]+$/;
    if (!nameRegex.test(cleanName)) {
        return {
            isValid: false,
            error: 'Name can only contain letters, spaces, dots, and hyphens',
            severity: 'medium'
        };
    }
    // Check for suspicious patterns
    if (/(.)\1{4,}/.test(cleanName.replace(/\s/g, ''))) {
        return {
            isValid: false,
            error: 'Invalid name pattern detected',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validateIncome = (income)=>{
    const cleanIncome = income.replace(/[^\d]/g, '');
    const incomeNumber = parseInt(cleanIncome);
    if (!cleanIncome) {
        return {
            isValid: false,
            error: 'Income is required',
            severity: 'high'
        };
    }
    if (incomeNumber < 10000) {
        return {
            isValid: false,
            error: 'Minimum income should be ₹10,000',
            severity: 'medium'
        };
    }
    if (incomeNumber > 10000000) {
        return {
            isValid: false,
            error: 'Income seems unusually high',
            severity: 'medium'
        };
    }
    return {
        isValid: true
    };
};
const validateAddress = (address)=>{
    const cleanAddress = address.trim();
    if (!cleanAddress) {
        return {
            isValid: false,
            error: 'Address is required',
            severity: 'high'
        };
    }
    if (cleanAddress.length < 10) {
        return {
            isValid: false,
            error: 'Address must be at least 10 characters',
            severity: 'medium'
        };
    }
    if (cleanAddress.length > 200) {
        return {
            isValid: false,
            error: 'Address cannot exceed 200 characters',
            severity: 'medium'
        };
    }
    // Check for suspicious patterns
    if (/(.)\1{10,}/.test(cleanAddress.replace(/\s/g, ''))) {
        return {
            isValid: false,
            error: 'Invalid address pattern detected',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validatePincode = (pincode)=>{
    const cleanPincode = pincode.replace(/\D/g, '');
    if (!cleanPincode) {
        return {
            isValid: false,
            error: 'Pincode is required',
            severity: 'high'
        };
    }
    if (cleanPincode.length !== 6) {
        return {
            isValid: false,
            error: 'Pincode must be 6 digits',
            severity: 'high'
        };
    }
    // Check for invalid patterns
    if (/^[0-1]/.test(cleanPincode)) {
        return {
            isValid: false,
            error: 'Invalid pincode format',
            severity: 'medium'
        };
    }
    if (/^(\d)\1{5}$/.test(cleanPincode)) {
        return {
            isValid: false,
            error: 'Pincode cannot have all same digits',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validateCompanyName = (company)=>{
    const cleanCompany = company.trim();
    if (!cleanCompany) {
        return {
            isValid: false,
            error: 'Company name is required',
            severity: 'high'
        };
    }
    if (cleanCompany.length < 2) {
        return {
            isValid: false,
            error: 'Company name must be at least 2 characters',
            severity: 'medium'
        };
    }
    if (cleanCompany.length > 100) {
        return {
            isValid: false,
            error: 'Company name cannot exceed 100 characters',
            severity: 'medium'
        };
    }
    return {
        isValid: true
    };
};
const sanitizeInput = (input)=>{
    return input.trim().replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .replace(/\s+/g, ' '); // Normalize whitespace
};
const checkRateLimit = (key, maxAttempts = 5, windowMs = 300000)=>{
    const now = Date.now();
    const attempts = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || '[]');
    // Filter attempts within the time window
    const recentAttempts = attempts.filter((timestamp)=>now - timestamp < windowMs);
    if (recentAttempts.length >= maxAttempts) {
        return false; // Rate limit exceeded
    }
    // Add current attempt
    recentAttempts.push(now);
    localStorage.setItem(`rate_limit_${key}`, JSON.stringify(recentAttempts));
    return true;
};
const encryptSensitiveData = (data)=>{
    // Simple base64 encoding for demo (use proper encryption in production)
    return btoa(JSON.stringify(data));
};
const decryptSensitiveData = (encryptedData)=>{
    try {
        return JSON.parse(atob(encryptedData));
    } catch  {
        return null;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/SecurityContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SecurityProvider": (()=>SecurityProvider),
    "useSecurityContext": (()=>useSecurityContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/validation.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const SecurityContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useSecurityContext = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SecurityContext);
    if (!context) {
        throw new Error('useSecurityContext must be used within a SecurityProvider');
    }
    return context;
};
_s(useSecurityContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const SecurityProvider = ({ children })=>{
    _s1();
    const [isSecureSession, setIsSecureSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [sessionTimeout, setSessionTimeout] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(30 * 60 * 1000); // 30 minutes
    const [sessionStartTime, setSessionStartTime] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(Date.now());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SecurityProvider.useEffect": ()=>{
            // Initialize secure session
            setIsSecureSession(true);
            setSessionStartTime(Date.now());
            // Set up session timeout
            const timeoutId = setTimeout({
                "SecurityProvider.useEffect.timeoutId": ()=>{
                    clearSecureData();
                    setIsSecureSession(false);
                    alert('Session expired for security reasons. Please restart your application.');
                }
            }["SecurityProvider.useEffect.timeoutId"], sessionTimeout);
            // Cleanup on unmount
            return ({
                "SecurityProvider.useEffect": ()=>{
                    clearTimeout(timeoutId);
                }
            })["SecurityProvider.useEffect"];
        }
    }["SecurityProvider.useEffect"], [
        sessionTimeout
    ]);
    // Monitor for suspicious activity
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SecurityProvider.useEffect": ()=>{
            const handleVisibilityChange = {
                "SecurityProvider.useEffect.handleVisibilityChange": ()=>{
                    if (document.hidden) {
                        logSecurityEvent('Page hidden - potential security risk', 'medium');
                    }
                }
            }["SecurityProvider.useEffect.handleVisibilityChange"];
            const handleBeforeUnload = {
                "SecurityProvider.useEffect.handleBeforeUnload": ()=>{
                    clearSecureData();
                }
            }["SecurityProvider.useEffect.handleBeforeUnload"];
            document.addEventListener('visibilitychange', handleVisibilityChange);
            window.addEventListener('beforeunload', handleBeforeUnload);
            return ({
                "SecurityProvider.useEffect": ()=>{
                    document.removeEventListener('visibilitychange', handleVisibilityChange);
                    window.removeEventListener('beforeunload', handleBeforeUnload);
                }
            })["SecurityProvider.useEffect"];
        }
    }["SecurityProvider.useEffect"], []);
    const setSecureData = (key, data)=>{
        try {
            const encryptedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["encryptSensitiveData"])({
                data,
                timestamp: Date.now(),
                sessionId: sessionStartTime
            });
            // Use sessionStorage for sensitive data (cleared when tab closes)
            sessionStorage.setItem(`secure_${key}`, encryptedData);
            logSecurityEvent(`Secure data stored: ${key}`, 'low');
        } catch (error) {
            logSecurityEvent(`Failed to store secure data: ${key}`, 'high');
            console.error('Failed to store secure data:', error);
        }
    };
    const getSecureData = (key)=>{
        try {
            const encryptedData = sessionStorage.getItem(`secure_${key}`);
            if (!encryptedData) return null;
            const decryptedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decryptSensitiveData"])(encryptedData);
            if (!decryptedData) return null;
            // Verify session integrity
            if (decryptedData.sessionId !== sessionStartTime) {
                logSecurityEvent(`Session integrity check failed for: ${key}`, 'high');
                return null;
            }
            // Check data age (expire after 1 hour)
            if (Date.now() - decryptedData.timestamp > 60 * 60 * 1000) {
                logSecurityEvent(`Expired data access attempt: ${key}`, 'medium');
                sessionStorage.removeItem(`secure_${key}`);
                return null;
            }
            return decryptedData.data;
        } catch (error) {
            logSecurityEvent(`Failed to retrieve secure data: ${key}`, 'high');
            console.error('Failed to retrieve secure data:', error);
            return null;
        }
    };
    const clearSecureData = ()=>{
        try {
            // Clear all secure data from sessionStorage
            const keys = Object.keys(sessionStorage);
            keys.forEach((key)=>{
                if (key.startsWith('secure_')) {
                    sessionStorage.removeItem(key);
                }
            });
            // Clear rate limiting data
            const rateLimitKeys = Object.keys(localStorage);
            rateLimitKeys.forEach((key)=>{
                if (key.startsWith('rate_limit_')) {
                    localStorage.removeItem(key);
                }
            });
            logSecurityEvent('All secure data cleared', 'low');
        } catch (error) {
            logSecurityEvent('Failed to clear secure data', 'high');
            console.error('Failed to clear secure data:', error);
        }
    };
    const checkSecurity = ()=>{
        // Check if session is still valid
        if (!isSecureSession) return false;
        // Check session timeout
        if (Date.now() - sessionStartTime > sessionTimeout) {
            setIsSecureSession(false);
            clearSecureData();
            return false;
        }
        // Check for suspicious activity patterns
        const securityLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');
        const recentHighSeverityEvents = securityLogs.filter((log)=>log.severity === 'high' && Date.now() - log.timestamp < 5 * 60 * 1000);
        if (recentHighSeverityEvents.length > 3) {
            logSecurityEvent('Multiple high-severity security events detected', 'high');
            setIsSecureSession(false);
            clearSecureData();
            return false;
        }
        return true;
    };
    const logSecurityEvent = (event, severity)=>{
        try {
            const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');
            const newLog = {
                event,
                severity,
                timestamp: Date.now(),
                userAgent: navigator.userAgent,
                url: window.location.href
            };
            logs.push(newLog);
            // Keep only last 100 logs
            if (logs.length > 100) {
                logs.splice(0, logs.length - 100);
            }
            localStorage.setItem('security_logs', JSON.stringify(logs));
            // Alert on high severity events
            if (severity === 'high') {
                console.warn('High severity security event:', event);
            }
        } catch (error) {
            console.error('Failed to log security event:', error);
        }
    };
    const isRateLimited = (action)=>{
        return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["checkRateLimit"])(action);
    };
    const contextValue = {
        isSecureSession,
        sessionTimeout,
        setSecureData,
        getSecureData,
        clearSecureData,
        checkSecurity,
        logSecurityEvent,
        isRateLimited
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SecurityContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/SecurityContext.tsx",
        lineNumber: 219,
        columnNumber: 5
    }, this);
};
_s1(SecurityProvider, "VKyo8Z5seHSp/F5XWKeiiEBArJ0=");
_c = SecurityProvider;
var _c;
__turbopack_context__.k.register(_c, "SecurityProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/SecureInput.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SecureInput": (()=>SecureInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SecurityContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/SecurityContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const SecureInput = ({ type, value, onChange, onValidation, validator, placeholder, label, required = false, maxLength, className = '', isSecure = false, autoComplete = 'off', disabled = false })=>{
    _s();
    const [validationResult, setValidationResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isValid: true
    });
    const [isFocused, setIsFocused] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [attempts, setAttempts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const { logSecurityEvent, isRateLimited } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SecurityContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecurityContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SecureInput.useEffect": ()=>{
            if (validator && value) {
                const result = validator(value);
                setValidationResult(result);
                onValidation?.(result);
                if (!result.isValid && result.severity === 'high') {
                    logSecurityEvent(`Invalid input detected: ${result.error}`, 'high');
                }
            }
        }
    }["SecureInput.useEffect"], [
        value,
        validator,
        onValidation,
        logSecurityEvent
    ]);
    const handleChange = (e)=>{
        const newValue = e.target.value;
        // Rate limiting for security
        if (isRateLimited('input_change')) {
            logSecurityEvent('Rate limit exceeded for input changes', 'high');
            return;
        }
        // Track failed attempts
        if (validator) {
            const result = validator(newValue);
            if (!result.isValid) {
                setAttempts((prev)=>prev + 1);
                if (attempts > 5) {
                    logSecurityEvent('Multiple validation failures detected', 'medium');
                }
            } else {
                setAttempts(0);
            }
        }
        onChange(newValue);
    };
    const handleFocus = ()=>{
        setIsFocused(true);
        if (isSecure) {
            logSecurityEvent('Secure input focused', 'low');
        }
    };
    const handleBlur = ()=>{
        setIsFocused(false);
        if (isSecure && value) {
            logSecurityEvent('Secure input completed', 'low');
        }
    };
    const handlePaste = (e)=>{
        if (isSecure) {
            logSecurityEvent('Paste attempt on secure input', 'medium');
            e.preventDefault();
        }
    };
    const handleCopy = (e)=>{
        if (isSecure) {
            logSecurityEvent('Copy attempt on secure input', 'high');
            e.preventDefault();
        }
    };
    const getInputClassName = ()=>{
        let baseClass = `w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 transition-all duration-200 ${className}`;
        if (isSecure) {
            baseClass += ' secure-input';
        }
        if (validationResult.isValid === false) {
            baseClass += ' error-input';
        } else if (validationResult.isValid && value) {
            baseClass += ' success-input';
        } else {
            baseClass += ' border-gray-300 focus:border-orange-500';
        }
        if (disabled) {
            baseClass += ' bg-gray-100 cursor-not-allowed';
        }
        return baseClass;
    };
    const getSeverityIcon = (severity)=>{
        switch(severity){
            case 'high':
                return '🚨';
            case 'medium':
                return '⚠️';
            case 'low':
                return 'ℹ️';
            default:
                return '❌';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-2",
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                className: "block text-sm font-medium text-gray-700",
                children: [
                    label,
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500 ml-1",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 148,
                        columnNumber: 24
                    }, this),
                    isSecure && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded",
                        children: "🔒 Secure"
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 150,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/SecureInput.tsx",
                lineNumber: 146,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: type,
                        value: value,
                        onChange: handleChange,
                        onFocus: handleFocus,
                        onBlur: handleBlur,
                        onPaste: handlePaste,
                        onCopy: handleCopy,
                        placeholder: placeholder,
                        required: required,
                        maxLength: maxLength,
                        autoComplete: autoComplete,
                        disabled: disabled,
                        className: getInputClassName(),
                        spellCheck: false,
                        autoCorrect: "off",
                        autoCapitalize: "off"
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 158,
                        columnNumber: 9
                    }, this),
                    value && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute right-3 top-1/2 transform -translate-y-1/2",
                        children: validationResult.isValid ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-green-500 text-lg",
                            children: "✓"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SecureInput.tsx",
                            lineNumber: 181,
                            columnNumber: 15
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-500 text-lg",
                            children: "✗"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SecureInput.tsx",
                            lineNumber: 183,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 179,
                        columnNumber: 11
                    }, this),
                    isSecure && isFocused && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute -top-2 -right-2 w-4 h-4 bg-orange-500 rounded-full animate-pulse"
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 190,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/SecureInput.tsx",
                lineNumber: 157,
                columnNumber: 7
            }, this),
            !validationResult.isValid && validationResult.error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "error-text",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: getSeverityIcon(validationResult.severity)
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 197,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: validationResult.error
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 198,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/SecureInput.tsx",
                lineNumber: 196,
                columnNumber: 9
            }, this),
            attempts > 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-orange-600 text-sm flex items-center gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "⚠️"
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 205,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "Multiple validation attempts detected. Please verify your input."
                    }, void 0, false, {
                        fileName: "[project]/src/components/SecureInput.tsx",
                        lineNumber: 206,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/SecureInput.tsx",
                lineNumber: 204,
                columnNumber: 9
            }, this),
            isSecure && maxLength && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-xs text-gray-500 text-right",
                children: [
                    value.length,
                    "/",
                    maxLength
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/SecureInput.tsx",
                lineNumber: 212,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/SecureInput.tsx",
        lineNumber: 144,
        columnNumber: 5
    }, this);
};
_s(SecureInput, "ApsEW/Db3OdnlheDi2qxbP0nVbs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SecurityContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecurityContext"]
    ];
});
_c = SecureInput;
var _c;
__turbopack_context__.k.register(_c, "SecureInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/LoanApplicationWrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoanApplicationWrapper": (()=>LoanApplicationWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SecurityContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/SecurityContext.tsx [app-client] (ecmascript)");
'use client';
;
;
const LoanApplicationWrapper = ({ children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SecurityContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SecurityProvider"], {
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/LoanApplicationWrapper.tsx",
        lineNumber: 12,
        columnNumber: 5
    }, this);
};
_c = LoanApplicationWrapper;
var _c;
__turbopack_context__.k.register(_c, "LoanApplicationWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/loan-application/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PhoneVerificationPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SecureInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/SecureInput.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/validation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SecurityContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/SecurityContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LoanApplicationWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/LoanApplicationWrapper.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
function PhoneVerification() {
    _s();
    const [phoneNumber, setPhoneNumber] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [validationResult, setValidationResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isValid: true
    });
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { setSecureData, logSecurityEvent, isRateLimited } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SecurityContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecurityContext"])();
    const handleSubmit = async (e)=>{
        e.preventDefault();
        // Rate limiting check
        if (isRateLimited('phone_submit')) {
            logSecurityEvent('Phone submission rate limit exceeded', 'high');
            alert('Too many attempts. Please wait before trying again.');
            return;
        }
        // Validate phone number
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validatePhoneNumber"])(phoneNumber);
        if (!validation.isValid) {
            setValidationResult(validation);
            logSecurityEvent(`Invalid phone number attempt: ${validation.error}`, 'medium');
            return;
        }
        setIsLoading(true);
        logSecurityEvent('Phone verification initiated', 'low');
        // Simulate API call
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        // Store phone number securely
        setSecureData("phoneNumber", phoneNumber);
        router.push("/loan-application/phone");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50 flex flex-col",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "bg-white shadow-sm px-4 py-3 flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>router.back(),
                        className: "w-8 h-8 flex items-center justify-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-gray-600",
                            children: "←"
                        }, void 0, false, {
                            fileName: "[project]/src/app/loan-application/page.tsx",
                            lineNumber: 56,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/loan-application/page.tsx",
                        lineNumber: 51,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-white font-bold text-sm",
                                    children: "N"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/loan-application/page.tsx",
                                    lineNumber: 60,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/loan-application/page.tsx",
                                lineNumber: 59,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-orange-500 font-bold text-lg",
                                children: "Nira"
                            }, void 0, false, {
                                fileName: "[project]/src/app/loan-application/page.tsx",
                                lineNumber: 62,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/loan-application/page.tsx",
                        lineNumber: 58,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-8 h-8"
                    }, void 0, false, {
                        fileName: "[project]/src/app/loan-application/page.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/loan-application/page.tsx",
                lineNumber: 50,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white px-4 py-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm text-gray-600",
                                children: "Step 1 of 5"
                            }, void 0, false, {
                                fileName: "[project]/src/app/loan-application/page.tsx",
                                lineNumber: 70,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm text-gray-600",
                                children: "20%"
                            }, void 0, false, {
                                fileName: "[project]/src/app/loan-application/page.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/loan-application/page.tsx",
                        lineNumber: 69,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full bg-gray-200 rounded-full h-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-orange-500 h-2 rounded-full w-1/5"
                        }, void 0, false, {
                            fileName: "[project]/src/app/loan-application/page.tsx",
                            lineNumber: 74,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/loan-application/page.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/loan-application/page.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "flex-1 px-4 py-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md mx-auto",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-semibold text-gray-900 mb-2",
                            children: "Please Number Verification"
                        }, void 0, false, {
                            fileName: "[project]/src/app/loan-application/page.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 mb-8",
                            children: "We'll send you a verification code"
                        }, void 0, false, {
                            fileName: "[project]/src/app/loan-application/page.tsx",
                            lineNumber: 84,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                            onSubmit: handleSubmit,
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute inset-y-0 left-0 pl-3 flex items-center z-10",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-gray-500 text-sm",
                                                    children: "+91"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/loan-application/page.tsx",
                                                    lineNumber: 92,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/loan-application/page.tsx",
                                                lineNumber: 91,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SecureInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SecureInput"], {
                                                type: "tel",
                                                value: phoneNumber,
                                                onChange: (value)=>setPhoneNumber(value.replace(/\D/g, '').slice(0, 10)),
                                                onValidation: setValidationResult,
                                                validator: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validatePhoneNumber"],
                                                placeholder: "9048739567",
                                                label: "Mobile Number",
                                                required: true,
                                                maxLength: 10,
                                                isSecure: true,
                                                className: "pl-12",
                                                autoComplete: "tel"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/loan-application/page.tsx",
                                                lineNumber: 94,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/loan-application/page.tsx",
                                        lineNumber: 90,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/loan-application/page.tsx",
                                    lineNumber: 89,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "submit",
                                    disabled: !validationResult.isValid || phoneNumber.length !== 10 || isLoading,
                                    className: "w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-orange-600 transition-colors",
                                    children: isLoading ? "SENDING..." : "SEND OTP"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/loan-application/page.tsx",
                                    lineNumber: 111,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/loan-application/page.tsx",
                            lineNumber: 88,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-8 space-y-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-3 text-sm text-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-blue-600 text-xs",
                                                children: "🔒"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/loan-application/page.tsx",
                                                lineNumber: 123,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/loan-application/page.tsx",
                                            lineNumber: 122,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "Your data is safe and secure with us"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/loan-application/page.tsx",
                                            lineNumber: 125,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/loan-application/page.tsx",
                                    lineNumber: 121,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-3 text-sm text-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-blue-600 text-xs",
                                                children: "⚡"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/loan-application/page.tsx",
                                                lineNumber: 129,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/loan-application/page.tsx",
                                            lineNumber: 128,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "Quick verification process"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/loan-application/page.tsx",
                                            lineNumber: 131,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/loan-application/page.tsx",
                                    lineNumber: 127,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/loan-application/page.tsx",
                            lineNumber: 120,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/loan-application/page.tsx",
                    lineNumber: 80,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/loan-application/page.tsx",
                lineNumber: 79,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/loan-application/page.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, this);
}
_s(PhoneVerification, "5Uvjf6D/v6kxI03mJGYHYnTEq3w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SecurityContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecurityContext"]
    ];
});
_c = PhoneVerification;
function PhoneVerificationPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LoanApplicationWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoanApplicationWrapper"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PhoneVerification, {}, void 0, false, {
            fileName: "[project]/src/app/loan-application/page.tsx",
            lineNumber: 143,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/loan-application/page.tsx",
        lineNumber: 142,
        columnNumber: 5
    }, this);
}
_c1 = PhoneVerificationPage;
var _c, _c1;
__turbopack_context__.k.register(_c, "PhoneVerification");
__turbopack_context__.k.register(_c1, "PhoneVerificationPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/next/navigation.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/navigation.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=_9199c492._.js.map