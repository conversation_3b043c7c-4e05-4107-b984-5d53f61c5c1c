module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/utils/validation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Security validation utilities for loan application
__turbopack_context__.s({
    "checkRateLimit": (()=>checkRateLimit),
    "decryptSensitiveData": (()=>decryptSensitiveData),
    "encryptSensitiveData": (()=>encryptSensitiveData),
    "sanitizeInput": (()=>sanitizeInput),
    "validateAddress": (()=>validateAddress),
    "validateCompanyName": (()=>validateCompanyName),
    "validateIncome": (()=>validateIncome),
    "validateName": (()=>validateName),
    "validateOTP": (()=>validateOTP),
    "validatePAN": (()=>validatePAN),
    "validatePhoneNumber": (()=>validatePhoneNumber),
    "validatePincode": (()=>validatePincode)
});
const validatePhoneNumber = (phone)=>{
    // Remove all non-digits
    const cleanPhone = phone.replace(/\D/g, '');
    if (!cleanPhone) {
        return {
            isValid: false,
            error: 'Phone number is required',
            severity: 'high'
        };
    }
    if (cleanPhone.length !== 10) {
        return {
            isValid: false,
            error: 'Phone number must be 10 digits',
            severity: 'high'
        };
    }
    // Check for invalid patterns
    if (/^[0-5]/.test(cleanPhone)) {
        return {
            isValid: false,
            error: 'Invalid phone number format',
            severity: 'medium'
        };
    }
    // Check for repeated digits (security concern)
    if (/^(\d)\1{9}$/.test(cleanPhone)) {
        return {
            isValid: false,
            error: 'Phone number cannot have all same digits',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validateOTP = (otp)=>{
    const cleanOTP = otp.replace(/\D/g, '');
    if (!cleanOTP) {
        return {
            isValid: false,
            error: 'OTP is required',
            severity: 'high'
        };
    }
    if (cleanOTP.length !== 6) {
        return {
            isValid: false,
            error: 'OTP must be 6 digits',
            severity: 'high'
        };
    }
    // Check for sequential numbers (security concern)
    if (/123456|654321|111111|000000/.test(cleanOTP)) {
        return {
            isValid: false,
            error: 'Invalid OTP pattern',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validatePAN = (pan)=>{
    const cleanPAN = pan.replace(/[^A-Z0-9]/g, '').toUpperCase();
    if (!cleanPAN) {
        return {
            isValid: false,
            error: 'PAN is required',
            severity: 'high'
        };
    }
    if (cleanPAN.length !== 10) {
        return {
            isValid: false,
            error: 'PAN must be 10 characters',
            severity: 'high'
        };
    }
    // PAN format: **********
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    if (!panRegex.test(cleanPAN)) {
        return {
            isValid: false,
            error: 'Invalid PAN format (**********)',
            severity: 'high'
        };
    }
    // Check for invalid patterns
    if (/AAAAA|11111|00000/.test(cleanPAN)) {
        return {
            isValid: false,
            error: 'Invalid PAN pattern detected',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validateName = (name)=>{
    const cleanName = name.trim();
    if (!cleanName) {
        return {
            isValid: false,
            error: 'Name is required',
            severity: 'high'
        };
    }
    if (cleanName.length < 2) {
        return {
            isValid: false,
            error: 'Name must be at least 2 characters',
            severity: 'medium'
        };
    }
    if (cleanName.length > 100) {
        return {
            isValid: false,
            error: 'Name cannot exceed 100 characters',
            severity: 'medium'
        };
    }
    // Only allow letters, spaces, dots, and hyphens
    const nameRegex = /^[a-zA-Z\s.-]+$/;
    if (!nameRegex.test(cleanName)) {
        return {
            isValid: false,
            error: 'Name can only contain letters, spaces, dots, and hyphens',
            severity: 'medium'
        };
    }
    // Check for suspicious patterns
    if (/(.)\1{4,}/.test(cleanName.replace(/\s/g, ''))) {
        return {
            isValid: false,
            error: 'Invalid name pattern detected',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validateIncome = (income)=>{
    const cleanIncome = income.replace(/[^\d]/g, '');
    const incomeNumber = parseInt(cleanIncome);
    if (!cleanIncome) {
        return {
            isValid: false,
            error: 'Income is required',
            severity: 'high'
        };
    }
    if (incomeNumber < 10000) {
        return {
            isValid: false,
            error: 'Minimum income should be ₹10,000',
            severity: 'medium'
        };
    }
    if (incomeNumber > 10000000) {
        return {
            isValid: false,
            error: 'Income seems unusually high',
            severity: 'medium'
        };
    }
    return {
        isValid: true
    };
};
const validateAddress = (address)=>{
    const cleanAddress = address.trim();
    if (!cleanAddress) {
        return {
            isValid: false,
            error: 'Address is required',
            severity: 'high'
        };
    }
    if (cleanAddress.length < 10) {
        return {
            isValid: false,
            error: 'Address must be at least 10 characters',
            severity: 'medium'
        };
    }
    if (cleanAddress.length > 200) {
        return {
            isValid: false,
            error: 'Address cannot exceed 200 characters',
            severity: 'medium'
        };
    }
    // Check for suspicious patterns
    if (/(.)\1{10,}/.test(cleanAddress.replace(/\s/g, ''))) {
        return {
            isValid: false,
            error: 'Invalid address pattern detected',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validatePincode = (pincode)=>{
    const cleanPincode = pincode.replace(/\D/g, '');
    if (!cleanPincode) {
        return {
            isValid: false,
            error: 'Pincode is required',
            severity: 'high'
        };
    }
    if (cleanPincode.length !== 6) {
        return {
            isValid: false,
            error: 'Pincode must be 6 digits',
            severity: 'high'
        };
    }
    // Check for invalid patterns
    if (/^[0-1]/.test(cleanPincode)) {
        return {
            isValid: false,
            error: 'Invalid pincode format',
            severity: 'medium'
        };
    }
    if (/^(\d)\1{5}$/.test(cleanPincode)) {
        return {
            isValid: false,
            error: 'Pincode cannot have all same digits',
            severity: 'high'
        };
    }
    return {
        isValid: true
    };
};
const validateCompanyName = (company)=>{
    const cleanCompany = company.trim();
    if (!cleanCompany) {
        return {
            isValid: false,
            error: 'Company name is required',
            severity: 'high'
        };
    }
    if (cleanCompany.length < 2) {
        return {
            isValid: false,
            error: 'Company name must be at least 2 characters',
            severity: 'medium'
        };
    }
    if (cleanCompany.length > 100) {
        return {
            isValid: false,
            error: 'Company name cannot exceed 100 characters',
            severity: 'medium'
        };
    }
    return {
        isValid: true
    };
};
const sanitizeInput = (input)=>{
    return input.trim().replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .replace(/\s+/g, ' '); // Normalize whitespace
};
const checkRateLimit = (key, maxAttempts = 5, windowMs = 300000)=>{
    const now = Date.now();
    const attempts = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || '[]');
    // Filter attempts within the time window
    const recentAttempts = attempts.filter((timestamp)=>now - timestamp < windowMs);
    if (recentAttempts.length >= maxAttempts) {
        return false; // Rate limit exceeded
    }
    // Add current attempt
    recentAttempts.push(now);
    localStorage.setItem(`rate_limit_${key}`, JSON.stringify(recentAttempts));
    return true;
};
const encryptSensitiveData = (data)=>{
    // Simple base64 encoding for demo (use proper encryption in production)
    return btoa(JSON.stringify(data));
};
const decryptSensitiveData = (encryptedData)=>{
    try {
        return JSON.parse(atob(encryptedData));
    } catch  {
        return null;
    }
};
}}),
"[project]/src/contexts/SecurityContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SecurityProvider": (()=>SecurityProvider),
    "useSecurityContext": (()=>useSecurityContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/validation.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const SecurityContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useSecurityContext = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(SecurityContext);
    if (!context) {
        throw new Error('useSecurityContext must be used within a SecurityProvider');
    }
    return context;
};
const SecurityProvider = ({ children })=>{
    const [isSecureSession, setIsSecureSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [sessionTimeout, setSessionTimeout] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(30 * 60 * 1000); // 30 minutes
    const [sessionStartTime, setSessionStartTime] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(Date.now());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Initialize secure session
        setIsSecureSession(true);
        setSessionStartTime(Date.now());
        // Set up session timeout
        const timeoutId = setTimeout(()=>{
            clearSecureData();
            setIsSecureSession(false);
            alert('Session expired for security reasons. Please restart your application.');
        }, sessionTimeout);
        // Cleanup on unmount
        return ()=>{
            clearTimeout(timeoutId);
        };
    }, [
        sessionTimeout
    ]);
    // Monitor for suspicious activity
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleVisibilityChange = ()=>{
            if (document.hidden) {
                logSecurityEvent('Page hidden - potential security risk', 'medium');
            }
        };
        const handleBeforeUnload = ()=>{
            clearSecureData();
        };
        document.addEventListener('visibilitychange', handleVisibilityChange);
        window.addEventListener('beforeunload', handleBeforeUnload);
        return ()=>{
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, []);
    const setSecureData = (key, data)=>{
        try {
            const encryptedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["encryptSensitiveData"])({
                data,
                timestamp: Date.now(),
                sessionId: sessionStartTime
            });
            // Use sessionStorage for sensitive data (cleared when tab closes)
            sessionStorage.setItem(`secure_${key}`, encryptedData);
            logSecurityEvent(`Secure data stored: ${key}`, 'low');
        } catch (error) {
            logSecurityEvent(`Failed to store secure data: ${key}`, 'high');
            console.error('Failed to store secure data:', error);
        }
    };
    const getSecureData = (key)=>{
        try {
            const encryptedData = sessionStorage.getItem(`secure_${key}`);
            if (!encryptedData) return null;
            const decryptedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["decryptSensitiveData"])(encryptedData);
            if (!decryptedData) return null;
            // Verify session integrity
            if (decryptedData.sessionId !== sessionStartTime) {
                logSecurityEvent(`Session integrity check failed for: ${key}`, 'high');
                return null;
            }
            // Check data age (expire after 1 hour)
            if (Date.now() - decryptedData.timestamp > 60 * 60 * 1000) {
                logSecurityEvent(`Expired data access attempt: ${key}`, 'medium');
                sessionStorage.removeItem(`secure_${key}`);
                return null;
            }
            return decryptedData.data;
        } catch (error) {
            logSecurityEvent(`Failed to retrieve secure data: ${key}`, 'high');
            console.error('Failed to retrieve secure data:', error);
            return null;
        }
    };
    const clearSecureData = ()=>{
        try {
            // Clear all secure data from sessionStorage
            const keys = Object.keys(sessionStorage);
            keys.forEach((key)=>{
                if (key.startsWith('secure_')) {
                    sessionStorage.removeItem(key);
                }
            });
            // Clear rate limiting data
            const rateLimitKeys = Object.keys(localStorage);
            rateLimitKeys.forEach((key)=>{
                if (key.startsWith('rate_limit_')) {
                    localStorage.removeItem(key);
                }
            });
            logSecurityEvent('All secure data cleared', 'low');
        } catch (error) {
            logSecurityEvent('Failed to clear secure data', 'high');
            console.error('Failed to clear secure data:', error);
        }
    };
    const checkSecurity = ()=>{
        // Check if session is still valid
        if (!isSecureSession) return false;
        // Check session timeout
        if (Date.now() - sessionStartTime > sessionTimeout) {
            setIsSecureSession(false);
            clearSecureData();
            return false;
        }
        // Check for suspicious activity patterns
        const securityLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');
        const recentHighSeverityEvents = securityLogs.filter((log)=>log.severity === 'high' && Date.now() - log.timestamp < 5 * 60 * 1000);
        if (recentHighSeverityEvents.length > 3) {
            logSecurityEvent('Multiple high-severity security events detected', 'high');
            setIsSecureSession(false);
            clearSecureData();
            return false;
        }
        return true;
    };
    const logSecurityEvent = (event, severity)=>{
        try {
            const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');
            const newLog = {
                event,
                severity,
                timestamp: Date.now(),
                userAgent: navigator.userAgent,
                url: window.location.href
            };
            logs.push(newLog);
            // Keep only last 100 logs
            if (logs.length > 100) {
                logs.splice(0, logs.length - 100);
            }
            localStorage.setItem('security_logs', JSON.stringify(logs));
            // Alert on high severity events
            if (severity === 'high') {
                console.warn('High severity security event:', event);
            }
        } catch (error) {
            console.error('Failed to log security event:', error);
        }
    };
    const isRateLimited = (action)=>{
        return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["checkRateLimit"])(action);
    };
    const contextValue = {
        isSecureSession,
        sessionTimeout,
        setSecureData,
        getSecureData,
        clearSecureData,
        checkSecurity,
        logSecurityEvent,
        isRateLimited
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SecurityContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/SecurityContext.tsx",
        lineNumber: 219,
        columnNumber: 5
    }, this);
};
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__51431732._.js.map