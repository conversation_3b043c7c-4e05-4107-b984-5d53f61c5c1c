{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/src/app/loan-application/success/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\n\nexport default function ApplicationSuccess() {\n  const [applicationData, setApplicationData] = useState<any>({});\n  const router = useRouter();\n\n  useEffect(() => {\n    const data = JSON.parse(localStorage.getItem(\"loanApplication\") || \"{}\");\n    setApplicationData(data);\n    \n    // If no application data, redirect to start\n    if (!data.applicationComplete) {\n      router.push(\"/\");\n    }\n  }, [router]);\n\n  const generateApplicationId = () => {\n    return \"NIRA\" + Date.now().toString().slice(-8);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm px-4 py-3 flex items-center justify-center\">\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center\">\n            <span className=\"text-white font-bold text-sm\">N</span>\n          </div>\n          <span className=\"text-orange-500 font-bold text-lg\">Nira</span>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1 px-4 py-6\">\n        <div className=\"max-w-md mx-auto text-center\">\n          {/* Success Icon */}\n          <div className=\"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n            <span className=\"text-green-600 text-3xl\">✓</span>\n          </div>\n\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            Application Submitted Successfully!\n          </h1>\n          \n          <p className=\"text-gray-600 mb-8\">\n            Your loan application has been received and is being processed.\n          </p>\n\n          {/* Application Details Card */}\n          <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6 text-left\">\n            <h2 className=\"font-semibold text-gray-900 mb-4\">Application Details</h2>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Application ID</span>\n                <span className=\"font-medium text-gray-900\">{generateApplicationId()}</span>\n              </div>\n              \n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Loan Amount</span>\n                <span className=\"font-medium text-gray-900\">₹3,50,000</span>\n              </div>\n              \n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Interest Rate</span>\n                <span className=\"font-medium text-gray-900\">24.96% p.a.</span>\n              </div>\n              \n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Processing Time</span>\n                <span className=\"font-medium text-gray-900\">24 hours</span>\n              </div>\n              \n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Status</span>\n                <span className=\"font-medium text-orange-600\">Under Review</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Next Steps */}\n          <div className=\"bg-blue-50 rounded-lg p-4 mb-6 text-left\">\n            <h3 className=\"font-semibold text-blue-900 mb-3\">What happens next?</h3>\n            <div className=\"space-y-2 text-sm text-blue-800\">\n              <div className=\"flex items-start gap-2\">\n                <span className=\"w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5\">1</span>\n                <span>We'll verify your documents and details</span>\n              </div>\n              <div className=\"flex items-start gap-2\">\n                <span className=\"w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5\">2</span>\n                <span>You'll receive approval notification within 24 hours</span>\n              </div>\n              <div className=\"flex items-start gap-2\">\n                <span className=\"w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5\">3</span>\n                <span>Loan amount will be disbursed to your bank account</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"bg-orange-50 rounded-lg p-4 mb-6\">\n            <h3 className=\"font-semibold text-orange-900 mb-2\">Need Help?</h3>\n            <div className=\"text-sm text-orange-800 space-y-1\">\n              <p>📞 Call us: 1800-123-4567</p>\n              <p>💬 WhatsApp: +91 98765 43210</p>\n              <p>📧 Email: <EMAIL></p>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"space-y-3\">\n            <Link\n              href=\"/\"\n              className=\"w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium block text-center hover:bg-orange-600 transition-colors\"\n            >\n              GO TO HOME\n            </Link>\n            \n            <button\n              onClick={() => {\n                const data = localStorage.getItem(\"loanApplication\");\n                if (data) {\n                  const blob = new Blob([data], { type: 'application/json' });\n                  const url = URL.createObjectURL(blob);\n                  const a = document.createElement('a');\n                  a.href = url;\n                  a.download = 'loan-application.json';\n                  a.click();\n                  URL.revokeObjectURL(url);\n                }\n              }}\n              className=\"w-full border border-orange-500 text-orange-500 py-3 px-4 rounded-lg font-medium hover:bg-orange-50 transition-colors\"\n            >\n              DOWNLOAD APPLICATION COPY\n            </button>\n          </div>\n\n          {/* Important Note */}\n          <div className=\"mt-8 text-xs text-gray-500 text-center\">\n            <p>Keep your application ID safe for future reference.</p>\n            <p>You'll receive SMS and email updates on your registered contact details.</p>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC7D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,sBAAsB;QACnE,mBAAmB;QAEnB,4CAA4C;QAC5C,IAAI,CAAC,KAAK,mBAAmB,EAAE;YAC7B,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,wBAAwB;QAC5B,OAAO,SAAS,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,8OAAC;4BAAK,WAAU;sCAAoC;;;;;;;;;;;;;;;;;0BAKxD,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA0B;;;;;;;;;;;sCAG5C,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAItD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAKlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;sCAMpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA+F;;;;;;8DAC/G,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA+F;;;;;;8DAC/G,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA+F;;;;;;8DAC/G,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAKP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,8OAAC;oCACC,SAAS;wCACP,MAAM,OAAO,aAAa,OAAO,CAAC;wCAClC,IAAI,MAAM;4CACR,MAAM,OAAO,IAAI,KAAK;gDAAC;6CAAK,EAAE;gDAAE,MAAM;4CAAmB;4CACzD,MAAM,MAAM,IAAI,eAAe,CAAC;4CAChC,MAAM,IAAI,SAAS,aAAa,CAAC;4CACjC,EAAE,IAAI,GAAG;4CACT,EAAE,QAAQ,GAAG;4CACb,EAAE,KAAK;4CACP,IAAI,eAAe,CAAC;wCACtB;oCACF;oCACA,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}