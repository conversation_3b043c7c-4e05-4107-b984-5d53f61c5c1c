{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/src/utils/validation.ts"], "sourcesContent": ["// Security validation utilities for loan application\n\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n  severity?: 'low' | 'medium' | 'high';\n}\n\n// Phone number validation\nexport const validatePhoneNumber = (phone: string): ValidationResult => {\n  // Remove all non-digits\n  const cleanPhone = phone.replace(/\\D/g, '');\n  \n  if (!cleanPhone) {\n    return { isValid: false, error: 'Phone number is required', severity: 'high' };\n  }\n  \n  if (cleanPhone.length !== 10) {\n    return { isValid: false, error: 'Phone number must be 10 digits', severity: 'high' };\n  }\n  \n  // Check for invalid patterns\n  if (/^[0-5]/.test(cleanPhone)) {\n    return { isValid: false, error: 'Invalid phone number format', severity: 'medium' };\n  }\n  \n  // Check for repeated digits (security concern)\n  if (/^(\\d)\\1{9}$/.test(cleanPhone)) {\n    return { isValid: false, error: 'Phone number cannot have all same digits', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// OTP validation\nexport const validateOTP = (otp: string): ValidationResult => {\n  const cleanOTP = otp.replace(/\\D/g, '');\n  \n  if (!cleanOTP) {\n    return { isValid: false, error: 'OTP is required', severity: 'high' };\n  }\n  \n  if (cleanOTP.length !== 6) {\n    return { isValid: false, error: 'OTP must be 6 digits', severity: 'high' };\n  }\n  \n  // Check for sequential numbers (security concern)\n  if (/123456|654321|111111|000000/.test(cleanOTP)) {\n    return { isValid: false, error: 'Invalid OTP pattern', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// PAN validation with enhanced security\nexport const validatePAN = (pan: string): ValidationResult => {\n  const cleanPAN = pan.replace(/[^A-Z0-9]/g, '').toUpperCase();\n  \n  if (!cleanPAN) {\n    return { isValid: false, error: 'PAN is required', severity: 'high' };\n  }\n  \n  if (cleanPAN.length !== 10) {\n    return { isValid: false, error: 'PAN must be 10 characters', severity: 'high' };\n  }\n  \n  // PAN format: **********\n  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;\n  if (!panRegex.test(cleanPAN)) {\n    return { isValid: false, error: 'Invalid PAN format (**********)', severity: 'high' };\n  }\n  \n  // Check for invalid patterns\n  if (/AAAAA|11111|00000/.test(cleanPAN)) {\n    return { isValid: false, error: 'Invalid PAN pattern detected', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// Name validation\nexport const validateName = (name: string): ValidationResult => {\n  const cleanName = name.trim();\n  \n  if (!cleanName) {\n    return { isValid: false, error: 'Name is required', severity: 'high' };\n  }\n  \n  if (cleanName.length < 2) {\n    return { isValid: false, error: 'Name must be at least 2 characters', severity: 'medium' };\n  }\n  \n  if (cleanName.length > 100) {\n    return { isValid: false, error: 'Name cannot exceed 100 characters', severity: 'medium' };\n  }\n  \n  // Only allow letters, spaces, dots, and hyphens\n  const nameRegex = /^[a-zA-Z\\s.-]+$/;\n  if (!nameRegex.test(cleanName)) {\n    return { isValid: false, error: 'Name can only contain letters, spaces, dots, and hyphens', severity: 'medium' };\n  }\n  \n  // Check for suspicious patterns\n  if (/(.)\\1{4,}/.test(cleanName.replace(/\\s/g, ''))) {\n    return { isValid: false, error: 'Invalid name pattern detected', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// Income validation\nexport const validateIncome = (income: string): ValidationResult => {\n  const cleanIncome = income.replace(/[^\\d]/g, '');\n  const incomeNumber = parseInt(cleanIncome);\n  \n  if (!cleanIncome) {\n    return { isValid: false, error: 'Income is required', severity: 'high' };\n  }\n  \n  if (incomeNumber < 10000) {\n    return { isValid: false, error: 'Minimum income should be ₹10,000', severity: 'medium' };\n  }\n  \n  if (incomeNumber > 10000000) {\n    return { isValid: false, error: 'Income seems unusually high', severity: 'medium' };\n  }\n  \n  return { isValid: true };\n};\n\n// Address validation\nexport const validateAddress = (address: string): ValidationResult => {\n  const cleanAddress = address.trim();\n  \n  if (!cleanAddress) {\n    return { isValid: false, error: 'Address is required', severity: 'high' };\n  }\n  \n  if (cleanAddress.length < 10) {\n    return { isValid: false, error: 'Address must be at least 10 characters', severity: 'medium' };\n  }\n  \n  if (cleanAddress.length > 200) {\n    return { isValid: false, error: 'Address cannot exceed 200 characters', severity: 'medium' };\n  }\n  \n  // Check for suspicious patterns\n  if (/(.)\\1{10,}/.test(cleanAddress.replace(/\\s/g, ''))) {\n    return { isValid: false, error: 'Invalid address pattern detected', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// Pincode validation\nexport const validatePincode = (pincode: string): ValidationResult => {\n  const cleanPincode = pincode.replace(/\\D/g, '');\n  \n  if (!cleanPincode) {\n    return { isValid: false, error: 'Pincode is required', severity: 'high' };\n  }\n  \n  if (cleanPincode.length !== 6) {\n    return { isValid: false, error: 'Pincode must be 6 digits', severity: 'high' };\n  }\n  \n  // Check for invalid patterns\n  if (/^[0-1]/.test(cleanPincode)) {\n    return { isValid: false, error: 'Invalid pincode format', severity: 'medium' };\n  }\n  \n  if (/^(\\d)\\1{5}$/.test(cleanPincode)) {\n    return { isValid: false, error: 'Pincode cannot have all same digits', severity: 'high' };\n  }\n  \n  return { isValid: true };\n};\n\n// Company name validation\nexport const validateCompanyName = (company: string): ValidationResult => {\n  const cleanCompany = company.trim();\n  \n  if (!cleanCompany) {\n    return { isValid: false, error: 'Company name is required', severity: 'high' };\n  }\n  \n  if (cleanCompany.length < 2) {\n    return { isValid: false, error: 'Company name must be at least 2 characters', severity: 'medium' };\n  }\n  \n  if (cleanCompany.length > 100) {\n    return { isValid: false, error: 'Company name cannot exceed 100 characters', severity: 'medium' };\n  }\n  \n  return { isValid: true };\n};\n\n// Data sanitization\nexport const sanitizeInput = (input: string): string => {\n  return input\n    .trim()\n    .replace(/[<>]/g, '') // Remove potential HTML tags\n    .replace(/['\"]/g, '') // Remove quotes\n    .replace(/\\s+/g, ' '); // Normalize whitespace\n};\n\n// Rate limiting check (simple implementation)\nexport const checkRateLimit = (key: string, maxAttempts: number = 5, windowMs: number = 300000): boolean => {\n  const now = Date.now();\n  const attempts = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || '[]');\n  \n  // Filter attempts within the time window\n  const recentAttempts = attempts.filter((timestamp: number) => now - timestamp < windowMs);\n  \n  if (recentAttempts.length >= maxAttempts) {\n    return false; // Rate limit exceeded\n  }\n  \n  // Add current attempt\n  recentAttempts.push(now);\n  localStorage.setItem(`rate_limit_${key}`, JSON.stringify(recentAttempts));\n  \n  return true;\n};\n\n// Encrypt sensitive data before storing\nexport const encryptSensitiveData = (data: any): string => {\n  // Simple base64 encoding for demo (use proper encryption in production)\n  return btoa(JSON.stringify(data));\n};\n\n// Decrypt sensitive data\nexport const decryptSensitiveData = (encryptedData: string): any => {\n  try {\n    return JSON.parse(atob(encryptedData));\n  } catch {\n    return null;\n  }\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;AAS9C,MAAM,sBAAsB,CAAC;IAClC,wBAAwB;IACxB,MAAM,aAAa,MAAM,OAAO,CAAC,OAAO;IAExC,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4B,UAAU;QAAO;IAC/E;IAEA,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;YAAkC,UAAU;QAAO;IACrF;IAEA,6BAA6B;IAC7B,IAAI,SAAS,IAAI,CAAC,aAAa;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA+B,UAAU;QAAS;IACpF;IAEA,+CAA+C;IAC/C,IAAI,cAAc,IAAI,CAAC,aAAa;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4C,UAAU;QAAO;IAC/F;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,cAAc,CAAC;IAC1B,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;IAEpC,IAAI,CAAC,UAAU;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;YAAmB,UAAU;QAAO;IACtE;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAwB,UAAU;QAAO;IAC3E;IAEA,kDAAkD;IAClD,IAAI,8BAA8B,IAAI,CAAC,WAAW;QAChD,OAAO;YAAE,SAAS;YAAO,OAAO;YAAuB,UAAU;QAAO;IAC1E;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,cAAc,CAAC;IAC1B,MAAM,WAAW,IAAI,OAAO,CAAC,cAAc,IAAI,WAAW;IAE1D,IAAI,CAAC,UAAU;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;YAAmB,UAAU;QAAO;IACtE;IAEA,IAAI,SAAS,MAAM,KAAK,IAAI;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA6B,UAAU;QAAO;IAChF;IAEA,yBAAyB;IACzB,MAAM,WAAW;IACjB,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;YAAmC,UAAU;QAAO;IACtF;IAEA,6BAA6B;IAC7B,IAAI,oBAAoB,IAAI,CAAC,WAAW;QACtC,OAAO;YAAE,SAAS;YAAO,OAAO;YAAgC,UAAU;QAAO;IACnF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,YAAY,KAAK,IAAI;IAE3B,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;YAAoB,UAAU;QAAO;IACvE;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAsC,UAAU;QAAS;IAC3F;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;YAAqC,UAAU;QAAS;IAC1F;IAEA,gDAAgD;IAChD,MAAM,YAAY;IAClB,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4D,UAAU;QAAS;IACjH;IAEA,gCAAgC;IAChC,IAAI,YAAY,IAAI,CAAC,UAAU,OAAO,CAAC,OAAO,MAAM;QAClD,OAAO;YAAE,SAAS;YAAO,OAAO;YAAiC,UAAU;QAAO;IACpF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,cAAc,OAAO,OAAO,CAAC,UAAU;IAC7C,MAAM,eAAe,SAAS;IAE9B,IAAI,CAAC,aAAa;QAChB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAsB,UAAU;QAAO;IACzE;IAEA,IAAI,eAAe,OAAO;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAoC,UAAU;QAAS;IACzF;IAEA,IAAI,eAAe,UAAU;QAC3B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA+B,UAAU;QAAS;IACpF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,eAAe,QAAQ,IAAI;IAEjC,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAuB,UAAU;QAAO;IAC1E;IAEA,IAAI,aAAa,MAAM,GAAG,IAAI;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA0C,UAAU;QAAS;IAC/F;IAEA,IAAI,aAAa,MAAM,GAAG,KAAK;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;YAAwC,UAAU;QAAS;IAC7F;IAEA,gCAAgC;IAChC,IAAI,aAAa,IAAI,CAAC,aAAa,OAAO,CAAC,OAAO,MAAM;QACtD,OAAO;YAAE,SAAS;YAAO,OAAO;YAAoC,UAAU;QAAO;IACvF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,eAAe,QAAQ,OAAO,CAAC,OAAO;IAE5C,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;YAAuB,UAAU;QAAO;IAC1E;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4B,UAAU;QAAO;IAC/E;IAEA,6BAA6B;IAC7B,IAAI,SAAS,IAAI,CAAC,eAAe;QAC/B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA0B,UAAU;QAAS;IAC/E;IAEA,IAAI,cAAc,IAAI,CAAC,eAAe;QACpC,OAAO;YAAE,SAAS;YAAO,OAAO;YAAuC,UAAU;QAAO;IAC1F;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI;IAEjC,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;YAA4B,UAAU;QAAO;IAC/E;IAEA,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA8C,UAAU;QAAS;IACnG;IAEA,IAAI,aAAa,MAAM,GAAG,KAAK;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;YAA6C,UAAU;QAAS;IAClG;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,SAAS,IAAI,6BAA6B;KAClD,OAAO,CAAC,SAAS,IAAI,gBAAgB;KACrC,OAAO,CAAC,QAAQ,MAAM,uBAAuB;AAClD;AAGO,MAAM,iBAAiB,CAAC,KAAa,cAAsB,CAAC,EAAE,WAAmB,MAAM;IAC5F,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK;IAEzE,yCAAyC;IACzC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,YAAsB,MAAM,YAAY;IAEhF,IAAI,eAAe,MAAM,IAAI,aAAa;QACxC,OAAO,OAAO,sBAAsB;IACtC;IAEA,sBAAsB;IACtB,eAAe,IAAI,CAAC;IACpB,aAAa,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,SAAS,CAAC;IAEzD,OAAO;AACT;AAGO,MAAM,uBAAuB,CAAC;IACnC,wEAAwE;IACxE,OAAO,KAAK,KAAK,SAAS,CAAC;AAC7B;AAGO,MAAM,uBAAuB,CAAC;IACnC,IAAI;QACF,OAAO,KAAK,KAAK,CAAC,KAAK;IACzB,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/src/contexts/SecurityContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { encryptSensitiveData, decryptSensitiveData, checkRateLimit } from '@/utils/validation';\n\ninterface SecurityContextType {\n  isSecureSession: boolean;\n  sessionTimeout: number;\n  setSecureData: (key: string, data: any) => void;\n  getSecureData: (key: string) => any;\n  clearSecureData: () => void;\n  checkSecurity: () => boolean;\n  logSecurityEvent: (event: string, severity: 'low' | 'medium' | 'high') => void;\n  isRateLimited: (action: string) => boolean;\n}\n\nconst SecurityContext = createContext<SecurityContextType | undefined>(undefined);\n\nexport const useSecurityContext = () => {\n  const context = useContext(SecurityContext);\n  if (!context) {\n    throw new Error('useSecurityContext must be used within a SecurityProvider');\n  }\n  return context;\n};\n\ninterface SecurityProviderProps {\n  children: React.ReactNode;\n}\n\nexport const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {\n  const [isSecureSession, setIsSecureSession] = useState(false);\n  const [sessionTimeout, setSessionTimeout] = useState(30 * 60 * 1000); // 30 minutes\n  const [sessionStartTime, setSessionStartTime] = useState(Date.now());\n\n  useEffect(() => {\n    // Initialize secure session\n    setIsSecureSession(true);\n    setSessionStartTime(Date.now());\n\n    // Set up session timeout\n    const timeoutId = setTimeout(() => {\n      clearSecureData();\n      setIsSecureSession(false);\n      alert('Session expired for security reasons. Please restart your application.');\n    }, sessionTimeout);\n\n    // Cleanup on unmount\n    return () => {\n      clearTimeout(timeoutId);\n    };\n  }, [sessionTimeout]);\n\n  // Monitor for suspicious activity\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden) {\n        logSecurityEvent('Page hidden - potential security risk', 'medium');\n      }\n    };\n\n    const handleBeforeUnload = () => {\n      clearSecureData();\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    return () => {\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, []);\n\n  const setSecureData = (key: string, data: any) => {\n    try {\n      const encryptedData = encryptSensitiveData({\n        data,\n        timestamp: Date.now(),\n        sessionId: sessionStartTime\n      });\n      \n      // Use sessionStorage for sensitive data (cleared when tab closes)\n      sessionStorage.setItem(`secure_${key}`, encryptedData);\n      \n      logSecurityEvent(`Secure data stored: ${key}`, 'low');\n    } catch (error) {\n      logSecurityEvent(`Failed to store secure data: ${key}`, 'high');\n      console.error('Failed to store secure data:', error);\n    }\n  };\n\n  const getSecureData = (key: string) => {\n    try {\n      const encryptedData = sessionStorage.getItem(`secure_${key}`);\n      if (!encryptedData) return null;\n\n      const decryptedData = decryptSensitiveData(encryptedData);\n      if (!decryptedData) return null;\n\n      // Verify session integrity\n      if (decryptedData.sessionId !== sessionStartTime) {\n        logSecurityEvent(`Session integrity check failed for: ${key}`, 'high');\n        return null;\n      }\n\n      // Check data age (expire after 1 hour)\n      if (Date.now() - decryptedData.timestamp > 60 * 60 * 1000) {\n        logSecurityEvent(`Expired data access attempt: ${key}`, 'medium');\n        sessionStorage.removeItem(`secure_${key}`);\n        return null;\n      }\n\n      return decryptedData.data;\n    } catch (error) {\n      logSecurityEvent(`Failed to retrieve secure data: ${key}`, 'high');\n      console.error('Failed to retrieve secure data:', error);\n      return null;\n    }\n  };\n\n  const clearSecureData = () => {\n    try {\n      // Clear all secure data from sessionStorage\n      const keys = Object.keys(sessionStorage);\n      keys.forEach(key => {\n        if (key.startsWith('secure_')) {\n          sessionStorage.removeItem(key);\n        }\n      });\n      \n      // Clear rate limiting data\n      const rateLimitKeys = Object.keys(localStorage);\n      rateLimitKeys.forEach(key => {\n        if (key.startsWith('rate_limit_')) {\n          localStorage.removeItem(key);\n        }\n      });\n\n      logSecurityEvent('All secure data cleared', 'low');\n    } catch (error) {\n      logSecurityEvent('Failed to clear secure data', 'high');\n      console.error('Failed to clear secure data:', error);\n    }\n  };\n\n  const checkSecurity = (): boolean => {\n    // Check if session is still valid\n    if (!isSecureSession) return false;\n\n    // Check session timeout\n    if (Date.now() - sessionStartTime > sessionTimeout) {\n      setIsSecureSession(false);\n      clearSecureData();\n      return false;\n    }\n\n    // Check for suspicious activity patterns\n    const securityLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');\n    const recentHighSeverityEvents = securityLogs.filter(\n      (log: any) => log.severity === 'high' && Date.now() - log.timestamp < 5 * 60 * 1000\n    );\n\n    if (recentHighSeverityEvents.length > 3) {\n      logSecurityEvent('Multiple high-severity security events detected', 'high');\n      setIsSecureSession(false);\n      clearSecureData();\n      return false;\n    }\n\n    return true;\n  };\n\n  const logSecurityEvent = (event: string, severity: 'low' | 'medium' | 'high') => {\n    try {\n      const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');\n      const newLog = {\n        event,\n        severity,\n        timestamp: Date.now(),\n        userAgent: navigator.userAgent,\n        url: window.location.href\n      };\n\n      logs.push(newLog);\n\n      // Keep only last 100 logs\n      if (logs.length > 100) {\n        logs.splice(0, logs.length - 100);\n      }\n\n      localStorage.setItem('security_logs', JSON.stringify(logs));\n\n      // Alert on high severity events\n      if (severity === 'high') {\n        console.warn('High severity security event:', event);\n      }\n    } catch (error) {\n      console.error('Failed to log security event:', error);\n    }\n  };\n\n  const isRateLimited = (action: string): boolean => {\n    return !checkRateLimit(action);\n  };\n\n  const contextValue: SecurityContextType = {\n    isSecureSession,\n    sessionTimeout,\n    setSecureData,\n    getSecureData,\n    clearSecureData,\n    checkSecurity,\n    logSecurityEvent,\n    isRateLimited\n  };\n\n  return (\n    <SecurityContext.Provider value={contextValue}>\n      {children}\n    </SecurityContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAgBA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,MAAM,qBAAqB;IAChC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,mBAAoD,CAAC,EAAE,QAAQ,EAAE;IAC5E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,OAAO,aAAa;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG;IAEjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,mBAAmB;QACnB,oBAAoB,KAAK,GAAG;QAE5B,yBAAyB;QACzB,MAAM,YAAY,WAAW;YAC3B;YACA,mBAAmB;YACnB,MAAM;QACR,GAAG;QAEH,qBAAqB;QACrB,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,IAAI,SAAS,MAAM,EAAE;gBACnB,iBAAiB,yCAAyC;YAC5D;QACF;QAEA,MAAM,qBAAqB;YACzB;QACF;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACL,SAAS,mBAAmB,CAAC,oBAAoB;YACjD,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC,KAAa;QAClC,IAAI;YACF,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzC;gBACA,WAAW,KAAK,GAAG;gBACnB,WAAW;YACb;YAEA,kEAAkE;YAClE,eAAe,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;YAExC,iBAAiB,CAAC,oBAAoB,EAAE,KAAK,EAAE;QACjD,EAAE,OAAO,OAAO;YACd,iBAAiB,CAAC,6BAA6B,EAAE,KAAK,EAAE;YACxD,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI;YACF,MAAM,gBAAgB,eAAe,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK;YAC5D,IAAI,CAAC,eAAe,OAAO;YAE3B,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;YAC3C,IAAI,CAAC,eAAe,OAAO;YAE3B,2BAA2B;YAC3B,IAAI,cAAc,SAAS,KAAK,kBAAkB;gBAChD,iBAAiB,CAAC,oCAAoC,EAAE,KAAK,EAAE;gBAC/D,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,KAAK,GAAG,KAAK,cAAc,SAAS,GAAG,KAAK,KAAK,MAAM;gBACzD,iBAAiB,CAAC,6BAA6B,EAAE,KAAK,EAAE;gBACxD,eAAe,UAAU,CAAC,CAAC,OAAO,EAAE,KAAK;gBACzC,OAAO;YACT;YAEA,OAAO,cAAc,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,iBAAiB,CAAC,gCAAgC,EAAE,KAAK,EAAE;YAC3D,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,4CAA4C;YAC5C,MAAM,OAAO,OAAO,IAAI,CAAC;YACzB,KAAK,OAAO,CAAC,CAAA;gBACX,IAAI,IAAI,UAAU,CAAC,YAAY;oBAC7B,eAAe,UAAU,CAAC;gBAC5B;YACF;YAEA,2BAA2B;YAC3B,MAAM,gBAAgB,OAAO,IAAI,CAAC;YAClC,cAAc,OAAO,CAAC,CAAA;gBACpB,IAAI,IAAI,UAAU,CAAC,gBAAgB;oBACjC,aAAa,UAAU,CAAC;gBAC1B;YACF;YAEA,iBAAiB,2BAA2B;QAC9C,EAAE,OAAO,OAAO;YACd,iBAAiB,+BAA+B;YAChD,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB;QACpB,kCAAkC;QAClC,IAAI,CAAC,iBAAiB,OAAO;QAE7B,wBAAwB;QACxB,IAAI,KAAK,GAAG,KAAK,mBAAmB,gBAAgB;YAClD,mBAAmB;YACnB;YACA,OAAO;QACT;QAEA,yCAAyC;QACzC,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,2BAA2B,aAAa,MAAM,CAClD,CAAC,MAAa,IAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,KAAK,IAAI,SAAS,GAAG,IAAI,KAAK;QAGjF,IAAI,yBAAyB,MAAM,GAAG,GAAG;YACvC,iBAAiB,mDAAmD;YACpE,mBAAmB;YACnB;YACA,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC,OAAe;QACvC,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;YACjE,MAAM,SAAS;gBACb;gBACA;gBACA,WAAW,KAAK,GAAG;gBACnB,WAAW,UAAU,SAAS;gBAC9B,KAAK,OAAO,QAAQ,CAAC,IAAI;YAC3B;YAEA,KAAK,IAAI,CAAC;YAEV,0BAA0B;YAC1B,IAAI,KAAK,MAAM,GAAG,KAAK;gBACrB,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,GAAG;YAC/B;YAEA,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,gCAAgC;YAChC,IAAI,aAAa,QAAQ;gBACvB,QAAQ,IAAI,CAAC,iCAAiC;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,CAAC,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE;IACzB;IAEA,MAAM,eAAoC;QACxC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Loan-Application-Sysytem/loan-application-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}